Break down a complex task into subtasks.

Arguments: $ARGUMENTS (task ID)

## Intelligent Task Expansion

Analyzes a task and creates detailed subtasks for better manageability.

## Execution

```bash
task-master expand --id=$ARGUMENTS
```

## Expansion Process

1. **Task Analysis**
   - Review task complexity
   - Identify components
   - Detect technical challenges
   - Estimate time requirements

2. **Subtask Generation**
   - Create 3-7 subtasks typically
   - Each subtask 1-4 hours
   - Logical implementation order
   - Clear acceptance criteria

3. **Smart Breakdown**
   - Setup/configuration tasks
   - Core implementation
   - Testing components
   - Integration steps
   - Documentation updates

## Enhanced Features

Based on task type:
- **Feature**: Setup → Implement → Test → Integrate
- **Bug Fix**: Reproduce → Diagnose → Fix → Verify
- **Refactor**: Analyze → Plan → Refactor → Validate

## Post-Expansion

After expansion:
1. Show subtask hierarchy
2. Update time estimates
3. Suggest implementation order
4. Highlight critical path