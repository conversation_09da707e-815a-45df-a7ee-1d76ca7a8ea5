# Overview  
这个主角技能将在主角周围生成一个持续的圆形能量场，对进入范围的怪物造成持续伤害。该技能具有高度的可配置性，包括伤害半径、伤害数值、视觉效果等，并且能量场会跟随玩家移动，为玩家提供稳定的范围防御能力。

# Core Features  
## 圆形能量场生成
- 在主角位置生成一个可见的圆形能量场
- 能量场具有可配置的半径大小
- 能量场跟随主角移动，始终以主角为中心

## 持续伤害系统
- 检测进入能量场范围内的敌对单位
- 对范围内的怪物每秒造成可配置的伤害
- 伤害计算基于配置的DPS值

## 可视化效果
- 可配置的能量场颜色（填充色）
- 可配置的边框颜色
- 平滑的视觉过渡效果
- 半透明效果以不遮挡游戏视野

## 配置系统
- 支持运行时配置所有关键参数
- 包括：半径大小、每秒伤害、颜色配置等

# User Experience  
## 玩家体验
- 玩家激活技能后立即看到围绕角色的能量场
- 能量场跟随玩家移动，提供持续的防御范围
- 怪物进入范围后受到持续伤害，有明确的视觉反馈
- 技能参数可通过配置界面调整

## 视觉反馈
- 清晰的圆形边界显示技能范围
- 怪物受到伤害时的视觉效果
- 流畅的跟随移动动画

# Technical Architecture  
## 系统组件
- **EnergyFieldSkill** - 主要技能组件类
- **EnergyFieldRenderer** - 能量场可视化组件  
- **DamageDetector** - 碰撞检测和伤害计算组件
- **SkillConfig** - 技能配置数据结构

## 数据模型
```gdscript
class_name EnergyFieldConfig
extends Resource

@export var radius: float = 100.0
@export var damage_per_second: float = 25.0
@export var field_color: Color = Color.CYAN
@export var border_color: Color = Color.WHITE
@export var border_width: float = 2.0
```

## 技术实现
- 使用 Godot 的 Area2D 进行碰撞检测
- Circle2D 或自定义 Canvas 进行圆形渲染
- Timer 节点处理每秒伤害计算
- 跟随系统通过每帧更新位置实现

# Development Roadmap  
## Phase 1: MVP 核心功能
- 基础能量场技能组件
- 简单的圆形可视化
- 基本的碰撞检测和伤害系统
- 跟随玩家移动功能

## Phase 2: 配置系统
- 完整的配置数据结构
- 运行时参数调整
- 配置文件保存/加载

## Phase 3: 视觉增强
- 高级视觉效果（渐变、动画等）
- 粒子效果
- 伤害数字显示
- 更精美的边框和填充效果

## Phase 4: 性能优化
- 批量伤害计算优化
- 渲染性能优化
- 内存使用优化

# Logical Dependency Chain
## 开发顺序
1. **基础框架搭建** - EnergyFieldSkill 类和基本组件结构
2. **碰撞检测系统** - Area2D 设置和敌人检测逻辑
3. **基础可视化** - 简单的圆形绘制（可用 ColorRect + 遮罩实现快速原型）
4. **跟随系统** - 能量场位置同步到玩家位置
5. **伤害计算** - 每秒伤害的计时器和伤害应用
6. **配置系统** - 参数化设计，使所有关键数值可配置
7. **视觉优化** - 改进渲染效果，添加边框、颜色等
8. **集成测试** - 与现有游戏系统集成

## 快速可用前端
- 先实现基本的白色圆圈显示技能范围
- 使用 ColorRect 节点快速创建可视化原型
- 优先保证功能性，视觉效果后续迭代

# Risks and Mitigations  
## 技术挑战
- **性能风险**: 大量敌人同时在范围内可能影响帧率
  - 缓解: 使用对象池，限制同时处理的敌人数量
- **碰撞检测精度**: Area2D 可能存在边缘情况
  - 缓解: 添加适当的碰撞容差，测试各种边界情况

## MVP 范围控制
- 优先实现基本功能，避免过度设计
- 视觉效果可以从简单开始，逐步增强
- 先验证核心伤害机制，再优化用户体验

## 资源约束
- 复用现有的伤害系统和敌人检测逻辑
- 使用 Godot 内置节点减少自定义渲染工作

# Appendix  
## 技术规格
- 目标平台: Godot 4.x
- 依赖系统: 现有的角色移动系统、敌人管理系统、伤害系统
- 预期性能: 支持同时对10+敌人造成伤害而不显著影响帧率

## 参考实现
- 参考现有的子弹系统的碰撞检测机制
- 参考现有的伤害数字显示系统
- 复用现有的配置系统架构