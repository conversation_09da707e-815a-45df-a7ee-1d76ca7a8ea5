# 回旋镖子弹系统产品需求文档 (PRD)

## 概述
实现一个高级回旋镖子弹系统，为 Snake 角色提供独特的远程攻击能力。回旋镖子弹具有椭圆形轨迹、自动返回、持续伤害等特性，为游戏增加策略性和视觉吸引力。

## 核心功能

### 1. 椭圆形轨迹运动
- **功能描述**: 回旋镖子弹沿椭圆形轨迹飞行，最终返回发射者
- **重要性**: 提供独特的攻击模式，增加游戏策略性
- **实现方式**: 新增 EllipticalMotionComponent 组件，实现椭圆轨迹算法

### 2. 自动返回机制
- **功能描述**: 子弹到达最远点后停留指定时间，然后自动返回发射者，碰撞墙壁需要立即返回
- **重要性**: 确保子弹不会丢失，提供持续的战斗能力
- **实现方式**: 状态机管理飞行、停留、返回三个阶段

### 3. 碰撞伤害系统
- **功能描述**: 子弹碰撞敌人造成伤害但不消失，可以穿透多个敌人
- **重要性**: 提供高效的群体伤害能力
- **实现方式**: 集成现有的 DamageComponent 和碰撞检测系统

### 4. 持续伤害机制
- **功能描述**: 子弹在停留期间对周围敌人造成持续伤害
- **重要性**: 增加战术价值，适合控制战场
- **实现方式**: 使用 DamageOverTimeComponent 实现区域持续伤害

### 5. 参数化配置
- **功能描述**: 可调整速度、轨迹参数、停留时间等属性
- **重要性**: 提供游戏平衡性和玩家自定义选项
- **实现方式**: 导出变量和配置文件系统

## 用户体验

### 用户角色
- **游戏玩家**: 使用回旋镖进行攻击和战术控制
- **游戏设计师**: 调整回旋镖参数平衡游戏性
- **开发者**: 扩展和修改回旋镖系统

### 关键用户流程
1. **发射阶段**: 朝玩家前方发射回旋镖，回旋镖返回后才能发射下一次
2. **飞行阶段**: 回旋镖沿椭圆轨迹飞行，造成伤害
3. **停留阶段**: 在最远点停留，造成持续伤害
4. **返回阶段**: 自动返回玩家，准备下次使用

### UI/UX 考虑
- 清晰的轨迹预览（可选）
- 伤害数字显示（可选）
- 返回倒计时指示器（可选）
- 参数调整界面（调试模式）

## 技术架构

### 系统组件
- **BoomerangBulletEntity**: 回旋镖实体，继承自 SnakeBulletEntity
- **EllipticalMotionComponent**: 椭圆轨迹运动组件
- **BoomerangStateComponent**: 状态管理组件
- **BoomerangConfig**: 配置数据资源

### 数据模型
```gdscript
# 回旋镖配置
class_name BoomerangConfig
extends Resource

@export var ellipticalRadiusX: float = 100.0      # 椭圆X轴半径
@export var ellipticalRadiusY: float = 60.0       # 椭圆Y轴半径
@export var forwardSpeed: float = 200.0           # 前进速度
@export var ellipticalSpeed: float = 3.0          # 椭圆运动速度
@export var maxDistance: float = 300.0            # 最大距离
@export var stayDuration: float = 1.0             # 停留时间
@export var returnSpeed: float = 250.0            # 返回速度
@export var damage: float = 25.0                  # 碰撞伤害
@export var damageOverTime: float = 10.0          # 持续伤害
@export var damageInterval: float = 0.2           # 持续伤害间隔
@export var damageRadius: float = 50.0            # 持续伤害半径
```

### API 和集成
- 与现有 Entity-Component 系统集成
- 使用现有的碰撞检测系统
- 集成现有的伤害和健康系统
- 支持现有的调试和日志系统

### 基础设施要求
- 基于现有的 Component 基类
- 使用现有的信号系统进行组件通信
- 支持现有的配置管理系统

## 开发路线图

### 阶段 1: 基础椭圆轨迹系统
- 创建 EllipticalMotionComponent
- 实现基本的椭圆轨迹算法
- 集成到 BoomerangBulletEntity
- 基础测试和调试

### 阶段 2: 状态管理和返回机制
- 实现 BoomerangStateComponent
- 添加停留和返回逻辑
- 集成自动返回功能
- 状态转换测试

### 阶段 3: 伤害系统集成
- 集成碰撞伤害系统 DamageComponent
- 实现持续伤害机制 DamageRepeatingComponent
- 伤害系统测试

### 阶段 4: 参数化和配置
- 创建 BoomerangConfig 资源
- 实现参数调整系统
- 添加调试和可视化功能
- 性能优化和平衡调整

### 阶段 5: 集成和测试
- 与现有 Snake 系统集成
- 完整功能测试
- 性能测试和优化
- 文档和示例创建

## 逻辑依赖链

### 基础依赖
1. **Entity-Component 系统**: 提供基础架构支持
2. **现有子弹系统**: 继承和扩展基础功能
3. **运动组件系统**: 基于现有运动组件扩展

### 功能依赖
1. **椭圆轨迹**: 基础运动系统
2. **状态管理**: 基于轨迹系统
3. **伤害系统**: 基于状态管理
4. **参数配置**: 基于所有功能实现

### 快速可用性
- 优先实现基础的椭圆轨迹
- 快速添加简单的返回机制
- 逐步完善伤害和配置系统

## 风险和缓解

### 技术挑战
- **椭圆轨迹计算复杂性**: 使用数学库和优化算法
- **状态管理复杂性**: 采用状态机模式，清晰的状态转换
- **性能影响**: 实现对象池和优化算法

### MVP 定义
- 基础椭圆轨迹 + 简单返回
- 基本碰撞伤害
- 最小参数配置

### 资源约束
- 复用现有组件系统
- 渐进式开发，分阶段实现
- 利用现有测试框架

## 附录

### 研究发现
- 基于现有的 CircularMotionComponent 架构
- 椭圆轨迹使用参数方程实现
- 状态机模式适合管理复杂行为

### 技术规格
- 支持 Godot 4.x
- 兼容现有的 Entity-Component 系统
- 支持导出参数调整
- 集成现有的调试系统

### 性能目标
- 单次发射内存占用 < 1MB
- 轨迹计算 < 0.1ms 每帧
- 支持同时存在 10+ 个回旋镖
- 60FPS 稳定运行
