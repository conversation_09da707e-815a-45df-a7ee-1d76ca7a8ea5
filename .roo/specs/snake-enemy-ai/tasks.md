# 蛇敌人AI行为实现任务

## 实现任务清单

- [ ] 1. 创建AI配置资源类
  - 创建 SnakeAIConfig.gd 资源类，定义所有AI行为参数
  - 包含巡逻点、检测范围、攻击范围、速度等配置项
  - _需求参考: 需求1-5 的所有配置参数_

- [ ] 2. 实现玩家检测条件节点
  - 2.1 创建 PlayerDetectedCondition.gd 继承 ConditionLeaf
    - 实现 tick() 方法检测玩家是否在范围内
    - 使用射线检测确认视线无障碍
    - 更新黑板中的玩家位置数据
    - _需求参考: 需求2.1, 2.2, 2.3_
    
  - 2.2 创建 PlayerInAttackRangeCondition.gd 继承 ConditionLeaf
    - 检查玩家是否在攻击距离内
    - 从黑板读取玩家位置计算距离
    - _需求参考: 需求4.1, 4.2_

- [ ] 3. 实现巡逻行为节点
  - 3.1 创建 PatrolAction.gd 继承 ActionLeaf
    - 实现在巡逻点之间的移动逻辑
    - 使用 OverheadPhysicsComponent 控制实体移动
    - 管理巡逻点索引和到达检测
    - _需求参考: 需求1.1, 1.2, 1.3, 1.4_
    
  - 3.2 集成动画控制
    - 在移动时调用 Animation8DirectionComponent 播放对应方向动画
    - 在巡逻点暂停时播放待机动画
    - _需求参考: 需求1.4_

- [ ] 4. 实现追击行为节点
  - 4.1 创建 ChasePlayerAction.gd 继承 ActionLeaf
    - 实现朝玩家位置的移动逻辑
    - 使用更快的追击速度
    - 当接近攻击距离时停止接近
    - _需求参考: 需求3.1, 3.2, 3.3, 3.4_
    
  - 4.2 添加智能路径规避
    - 避免卡在障碍物上的基本逻辑
    - 预测玩家移动方向进行拦截
    - _需求参考: 需求3.1_

- [ ] 5. 实现攻击行为节点
  - 5.1 创建 AttackPlayerAction.gd 继承 ActionLeaf
    - 使用 FireComponent 发射子弹攻击玩家
    - 瞄准玩家当前位置
    - 管理攻击冷却时间
    - _需求参考: 需求4.1, 4.2, 4.3, 4.4_
    
  - 5.2 集成攻击动画
    - 播放攻击动画
    - 确保动画与射击时机同步
    - _需求参考: 需求4.4_

- [ ] 6. 实现目标丢失处理节点
  - 6.1 创建 LostTargetAction.gd 继承 ActionLeaf
    - 检测玩家是否超出追击范围
    - 实现丢失目标后的等待逻辑
    - 返回最近巡逻点的行为
    - _需求参考: 需求5.1, 5.2, 5.3, 5.4_

- [ ] 7. 构建完整行为树结构
  - 7.1 创建主行为树场景文件
    - 创建 SnakeAIBehaviorTree.tscn
    - 配置根选择器节点
    - 按优先级添加行为序列：攻击 > 追击 > 巡逻
    - _需求参考: 所有需求的集成_
    
  - 7.2 配置行为序列
    - 创建攻击序列：PlayerInAttackRange + AttackPlayer
    - 创建追击序列：PlayerDetected + ChasePlayer  
    - 创建巡逻序列：PatrolAction
    - _需求参考: 所有需求的集成_

- [ ] 8. 集成到蛇敌人实体
  - 8.1 修改 EnemyEntity.tscn 场景
    - 添加 BeehaveTree 节点作为子节点
    - 实例化 SnakeAIBehaviorTree 作为行为树子树
    - 配置 actor 指向 EnemyEntity 根节点
    - _需求参考: 所有需求的集成_
    
  - 8.2 添加玩家检测区域
    - 添加 Area2D 子节点用于检测玩家进入
    - 配置碰撞形状为圆形检测范围
    - 连接 area_entered/area_exited 信号到行为树
    - _需求参考: 需求2的集成_
    
  - 8.3 创建 AI 配置实例
    - 创建 SnakeAIConfig 资源文件
    - 为蛇敌人配置合理的默认参数值
    - 在实体中引用配置文件
    - _需求参考: 所有需求的参数配置_

- [ ] 9. 实现黑板数据管理
  - 9.1 初始化黑板数据
    - 在 EnemyEntity.gd 中设置初始黑板值
    - 配置巡逻点、速度、范围等参数
    - _需求参考: 所有需求的数据共享_
    
  - 9.2 实现数据更新机制
    - 各个行为节点正确读写黑板数据
    - 确保数据一致性和同步
    - _需求参考: 所有需求的状态管理_

- [ ] 10. 添加调试和可视化功能
  - 10.1 实现调试可视化
    - 在编辑器中显示检测范围
    - 显示当前巡逻目标和AI状态
    - 添加调试日志输出
    - _需求参考: 设计文档的调试支持_
    
  - 10.2 创建调试控制面板
    - 允许运行时调整AI参数
    - 显示当前行为树执行状态
    - _需求参考: 设计文档的调试支持_

- [ ] 11. 编写单元测试
  - 11.1 测试条件节点
    - 测试玩家检测逻辑的准确性
    - 测试距离计算的正确性
    - _需求参考: 需求2, 需求4的验证_
    
  - 11.2 测试行动节点
    - 测试巡逻路径规划
    - 测试追击和攻击行为
    - _需求参考: 需求1, 需求3, 需求4的验证_

- [ ] 12. 集成测试和优化
  - 12.1 测试完整AI循环
    - 验证巡逻→检测→追击→攻击→丢失目标的完整流程
    - 测试边界条件和异常情况
    - _需求参考: 所有需求的集成验证_
    
  - 12.2 性能优化
    - 优化行为树执行频率
    - 减少不必要的计算开销
    - 确保多个敌人同时运行的性能
    - _需求参考: 设计文档的性能考虑_