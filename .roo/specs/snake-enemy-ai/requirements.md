# 蛇敌人AI行为需求文档

## 简介

为蛇敌人实体添加基于 beehave 行为树插件的AI行为系统，实现"巡逻 → 发现玩家 → 追击 → 射击攻击 → 失去目标返回巡逻"的基本AI循环。

## 需求

### 需求1：巡逻行为

**用户故事：** 作为一个蛇敌人，我想在指定区域内巡逻，以便守护我的领域并寻找入侵者。

#### 验收标准
1. 当没有发现玩家时，蛇敌人应该在预设的巡逻点之间移动
2. 当到达一个巡逻点时，蛇敌人应该暂停并转向下一个巡逻点
3. 巡逻移动应该使用现有的 OverheadPhysicsComponent 组件
4. 移动时应该播放适当的方向动画（使用 Animation8DirectionComponent）

### 需求2：玩家检测

**用户故事：** 作为一个蛇敌人，我想能够检测到进入我视野范围的玩家，以便做出相应的反应。

#### 验收标准
1. 当玩家进入检测范围时，蛇敌人应该立即察觉
2. 检测范围应该是一个可配置的圆形区域
3. 检测应该考虑障碍物遮挡（线性视野检测）
4. 检测到玩家后应该立即转换到追击状态

### 需求3：追击行为

**用户故事：** 作为一个蛇敌人，我想追击被发现的玩家，以便接近到攻击距离。

#### 验收标准
1. 当发现玩家后，蛇敌人应该朝玩家方向移动
2. 追击速度应该比巡逻速度更快
3. 当到达攻击距离时，应该停止接近并开始攻击
4. 追击时应该播放适当的移动动画

### 需求4：射击攻击

**用户故事：** 作为一个蛇敌人，我想攻击进入攻击范围的玩家，以便保护我的领域。

#### 验收标准
1. 当玩家在攻击范围内时，蛇敌人应该使用 FireComponent 发射子弹
2. 攻击前应该瞄准玩家位置
3. 攻击应该有冷却时间间隔
4. 攻击时应该播放攻击动画

### 需求5：目标丢失处理

**用户故事：** 作为一个蛇敌人，我想在失去玩家目标时返回巡逻状态，以便继续守护我的领域。

#### 验收标准
1. 当玩家超出追击范围时，蛇敌人应该失去目标
2. 失去目标后应该等待一段时间再返回巡逻
3. 返回巡逻时应该回到最近的巡逻点
4. 整个过程应该平滑过渡，无突兀的行为切换