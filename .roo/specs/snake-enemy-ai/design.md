# 蛇敌人AI行为设计文档

## 概述

本文档描述了如何使用 beehave 行为树插件为蛇敌人实体实现AI行为系统。系统将实现巡逻、玩家检测、追击、攻击和目标丢失处理的完整行为循环。

## 架构设计

### 核心组件架构

```mermaid
graph TB
    A[EnemyEntity.tscn] --> B[BeehaveTree]
    B --> C[SnakeAIBehaviorTree]
    C --> D[PatrolBehavior]
    C --> E[DetectPlayerBehavior] 
    C --> F[ChasePlayerBehavior]
    C --> G[AttackPlayerBehavior]
    A --> H[ExistingComponents]
    H --> I[FireComponent]
    H --> J[OverheadPhysicsComponent]
    H --> K[Animation8DirectionComponent]
    H --> L[AreaContactComponent]
```

### 行为树结构

主行为树采用选择器（Selector）模式，按优先级执行不同的行为：

```mermaid
graph TB
    Root[SnakeAI Selector] --> Combat[CombatSequence]
    Root --> Chase[ChaseSequence]
    Root --> Patrol[PatrolSequence]
    
    Combat --> IsPlayerInAttackRange{PlayerInAttackRange?}
    Combat --> AttackPlayer[AttackPlayerAction]
    
    Chase --> IsPlayerDetected{PlayerDetected?}
    Chase --> ChasePlayer[ChasePlayerAction]
    
    Patrol --> PatrolAction[PatrolAction]
```

## 组件和接口

### 自定义行为树节点

#### 1. 条件节点 (ConditionLeaf)

**PlayerInAttackRangeCondition**
- 检查玩家是否在攻击范围内
- 使用黑板存储的玩家位置数据
- 返回 SUCCESS/FAILURE

**PlayerDetectedCondition**
- 检查是否检测到玩家
- 结合视野范围和障碍物检测
- 更新黑板中的玩家位置

**IsPatrollingCondition**
- 检查是否应该继续巡逻
- 验证巡逻点数据有效性

#### 2. 行动节点 (ActionLeaf)

**PatrolAction**
- 在巡逻点之间移动
- 使用 OverheadPhysicsComponent 控制移动
- 更新 Animation8DirectionComponent 动画方向

**ChasePlayerAction**
- 追击检测到的玩家
- 动态调整移动速度
- 保持适当的攻击距离

**AttackPlayerAction**
- 使用 FireComponent 发射子弹
- 瞄准玩家当前位置
- 管理攻击冷却时间

**DetectPlayerAction**
- 持续扫描玩家
- 更新黑板中的检测状态
- 处理视野和障碍物

### 黑板数据结构

```gdscript
# AI状态数据
ai_state: String = "patrol"  # patrol, chase, attack, lost_target
current_patrol_index: int = 0
patrol_points: Array[Vector2] = []

# 玩家相关数据
player_detected: bool = false
player_position: Vector2 = Vector2.ZERO
player_last_seen_position: Vector2 = Vector2.ZERO
player_distance: float = 0.0
time_since_player_lost: float = 0.0

# 攻击相关数据
last_attack_time: float = 0.0
attack_cooldown: float = 1.0
attack_range: float = 200.0

# 检测相关数据
detection_range: float = 300.0
lost_target_timeout: float = 3.0

# 移动相关数据
patrol_speed: float = 100.0
chase_speed: float = 150.0
current_target_position: Vector2 = Vector2.ZERO
```

## 数据模型

### 配置参数

创建 `SnakeAIConfig` 资源类：

```gdscript
class_name SnakeAIConfig extends Resource

@export var patrol_points: Array[Vector2] = []
@export var detection_range: float = 300.0
@export var attack_range: float = 200.0
@export var patrol_speed: float = 100.0
@export var chase_speed: float = 150.0
@export var attack_cooldown: float = 1.0
@export var lost_target_timeout: float = 3.0
@export var patrol_wait_time: float = 2.0
```

### 玩家检测系统

使用 `AreaContactComponent` 或新建 `PlayerDetectionArea` 来：
- 检测进入范围的玩家实体
- 执行射线检测确认视线清晰
- 更新黑板中的检测状态

## 错误处理

### 异常情况处理

1. **缺失玩家目标**
   - 如果 GameState.players 为空，返回巡逻状态
   - 记录警告日志

2. **巡逻点配置错误**
   - 如果巡逻点数组为空，使用实体当前位置作为单点巡逻
   - 如果巡逻点索引越界，重置为0

3. **组件依赖缺失**
   - 检查必需组件是否存在
   - 提供降级行为（如静止不动）

4. **物理系统异常**
   - 如果移动组件失效，切换到待机状态
   - 确保不会卡在墙体中

### 调试支持

- 在 Debug 模式下可视化检测范围
- 显示当前AI状态和目标位置
- 记录状态转换日志

## 测试策略

### 单元测试

1. **条件节点测试**
   - 测试各种距离条件的判断准确性
   - 验证黑板数据读取正确性

2. **行动节点测试**
   - 测试巡逻路径规划
   - 验证攻击执行时机

### 集成测试

1. **完整行为循环测试**
   - 模拟玩家进入/离开检测范围
   - 验证状态转换的流畅性

2. **性能测试**
   - 多个蛇敌人同时运行的性能表现
   - 行为树执行频率优化

### 手动测试场景

1. **基础巡逻测试**
   - 蛇敌人在无玩家情况下正常巡逻

2. **检测和追击测试**
   - 玩家接近时正确检测和追击

3. **攻击行为测试**
   - 在攻击范围内正确发射子弹

4. **目标丢失测试**
   - 玩家远离时正确返回巡逻状态

### 边界条件测试

- 玩家在检测范围边缘的行为
- 多个玩家同时存在的处理
- 巡逻点重叠或无效的处理
- 地形障碍物对检测的影响