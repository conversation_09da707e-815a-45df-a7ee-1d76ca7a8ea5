# AI Behavior Component Design

## Overview

BehaviorComponent是一个基于beehave行为树插件的AI行为组件，集成到项目的Entity-Component框架中。该组件直接继承自BeehaveTree，提供智能行为控制功能，内置状态机逻辑用于管理复杂行为状态。

## Architecture

```mermaid
graph TB
    subgraph "Entity Scene Tree"
        Entity["Entity (Node2D)"]
        
        subgraph "Components"
            MovementComponent["MovementComponent"]
            AnimationComponent["AnimationComponent"]
            ShootingComponent["ShootingComponent"]
            OtherComponents["Other Components..."]
            BehaviorComponent["BehaviorComponent<br/>(BeehaveTree Root)"]
        end
        
        subgraph "External State Classes"
            GuardStates["Guard States<br/>(extends State)"]
            MonsterStates["Monster States<br/>(extends State)"]
            CustomStates["Custom States<br/>(extends State)"]
        end
    end
    
    Entity --> MovementComponent
    Entity --> AnimationComponent
    Entity --> ShootingComponent
    Entity --> OtherComponents
    Entity --> BehaviorComponent
```

## Components and Interfaces
### 场景结构
```
Entity.tscn
   ├── BehaviorComponent.tscn（根节点为BeehaveTree）
   │   └── SnakeBehaveScene.tscn (根节点为BehaveNode)       
   └── OtherComponent.tscn （其他组件）  
```         
### 组件脚本
```gdscript
class_name BehaviorComponent
extends Component

# Internal state machine logic
var current_state: String = ""
var state_data: Dictionary = {}
var state_instances: Dictionary = {}

# State management
var state_history: Array = []
var max_history_size: int = 10
var previous_state: String = ""

# Configuration
@export var external_blackboard: Blackboard
@export var states: Array[State] = []
@export var initial_state: String = ""
@export var auto_start: bool = true
@export var tick_rate: int = 1

# Component access helpers
func get_component(component_name: String):
    if actor:
        if actor.has_method("get_component"):
            return actor.get_component(component_name)
        elif actor.has_method("coComponents") and actor.coComponents:
            return actor.coComponents.get(component_name)
    return null

# Built-in state machine methods
func register_state_instance(state_instance: State) -> void:
    state_instances[state_instance.state_name] = state_instance
    state_instance.behavior_component = self

func load_states_from_config() -> void:
    if states.is_empty():
        push_warning("No states provided")
        return
    
    # 清空现有状态
    state_instances.clear()
    
    # 从配置加载状态
    for state_instance in states:
        if state_instance:
            register_state_instance(state_instance)
    
    # 设置初始状态
    if initial_state != "":
        var initial_state_instance = state_instances.get(initial_state)
        if initial_state_instance:
            change_state(initial_state_instance)

func change_state(new_state_instance: State, data: Dictionary = {}) -> bool:
    var new_state_name = new_state_instance.state_name
    if not state_instances.has(new_state_name):
        push_error("State not registered: " + new_state_name)
        return false
    
    # 退出当前状态
    if current_state != "":
        var current_state_instance = state_instances.get(current_state)
        if current_state_instance:
            current_state_instance.exit()
        
        previous_state = current_state
    
    # 进入新状态
    current_state = new_state_name
    state_data = data
    
    # 调用状态实例的enter方法
    new_state_instance.enter(data)
    
    # 记录状态历史
    _add_to_history(new_state_name)
    
    return true

func update_current_state() -> int:
    if current_state == "":
        return 0  # FAILURE
    
    # 使用状态实例的update方法
    var state_instance = state_instances.get(current_state)
    if state_instance:
        return state_instance.update()
    
    return 0  # FAILURE

func get_current_state_name() -> String:
    return current_state

func get_state_history() -> Array:
    return state_history.duplicate()

func is_in_state(state_name: String) -> bool:
    return current_state == state_name

func _add_to_history(state_name: String) -> void:
    state_history.append(state_name)
    if state_history.size() > max_history_size:
        state_history.pop_front()

# Safe blackboard access
func safe_blackboard_get(key: String, default_value = null):
    if blackboard:
        return blackboard.get_value(key, default_value)
    push_warning("Blackboard not available for key: " + key)
    return default_value

func safe_blackboard_set(key: String, value) -> void:
    if blackboard:
        blackboard.set_value(key, value)
    else:
        push_warning("Blackboard not available for setting key: " + key)

# 初始化函数
func _ready():
    # 设置actor为父实体
    actor = get_parent()
    
    # 配置外部Blackboard
    if external_blackboard:
        blackboard = external_blackboard
    
    # 设置组件引用到blackboard中
    if blackboard:
        blackboard.set_value("behavior_component", self)
        blackboard.set_value("entity", actor)
    
    # 从配置加载状态机
    load_states_from_config()
    
    # 自动启动
    if auto_start:
        enable()

### 状态基类设计

```gdscript
# 状态基类 - 定义单个状态的基本接口
class_name State
extends RefCounted

var state_name: String
var behavior_component: BehaviorComponent

func _init(name: String):
    state_name = name

# 进入状态时调用
func enter(data: Dictionary = {}) -> void:
    pass

# 状态更新时调用
func update() -> int:
    return 1  # SUCCESS

# 退出状态时调用
func exit() -> void:
    pass

# 安全访问blackboard - 通过behavior_component的公共方法
func safe_blackboard_get(key: String, default_value = null):
    if behavior_component:
        return behavior_component.safe_blackboard_get(key, default_value)
    return default_value

func safe_blackboard_set(key: String, value) -> void:
    if behavior_component:
        behavior_component.safe_blackboard_set(key, value)

# 通用组件获取方法
func get_component(component_name: String):
    if behavior_component and behavior_component.actor:
        var entity = behavior_component.actor
        if entity.has_method("get_component"):
            return entity.get_component(component_name)
        elif entity.has_method("coComponents") and entity.coComponents:
            return entity.coComponents.get(component_name)
    return null

# 获取Entity实例
# 用于直接访问Entity的属性和方法，如位置、旋转、缩放等
func get_entity():
    if behavior_component and behavior_component.actor:
        return behavior_component.actor
    return null

# 便捷组件获取方法（可选，用于常用组件）
func get_movement_component():
    return get_component("MovementComponent")

func get_animation_component():
    return get_component("AnimationComponent")

func get_shooting_component():
    return get_component("ShootingComponent")

# 状态切换辅助方法
func change_state(new_state_instance: State, data: Dictionary = {}) -> bool:
    if behavior_component:
        return behavior_component.change_state(new_state_instance, data)
    return false
```



### 状态使用示例

```gdscript
# 守卫状态示例
class_name GuardIdleState
extends State

func _init():
    super("guard_idle")
    
    func enter(data: Dictionary = {}) -> void:
        safe_blackboard_set("guard_state", "idle")
    
    func update() -> int:
        # 检查是否有可疑活动
        var detection_result = _check_for_threats()
        if detection_result.has("threat"):
            # 需要获取investigate状态实例
            var investigate_state = behavior_component.state_instances.get("guard_investigate")
            if investigate_state:
                change_state(investigate_state, detection_result)
            return 1  # SUCCESS
        
        # 随机开始巡逻
        if randf() < 0.01:  # 1% 概率开始巡逻
            # 需要获取patrol状态实例
            var patrol_state = behavior_component.state_instances.get("guard_patrol")
            if patrol_state:
                change_state(patrol_state)
            return 1  # SUCCESS
        
        return 2  # RUNNING
    
    func _check_for_threats() -> Dictionary:
        # 威胁检测逻辑
        return {}

# 怪物状态示例
class_name MonsterRoamState
extends State

func _init():
    super("monster_roam")
    
    func enter(data: Dictionary = {}) -> void:
        safe_blackboard_set("monster_state", "roam")
        safe_blackboard_set("roam_target", _get_random_roam_position())
    
    func update() -> int:
        # 检查玩家
        var player = _find_nearby_player()
        if player:
            # 需要获取hunt状态实例
            var hunt_state = behavior_component.state_instances.get("monster_hunt")
            if hunt_state:
                change_state(hunt_state, {"target": player})
            return 1  # SUCCESS
        
        # 游荡移动
        var movement = get_component("MovementComponent")
        var roam_target = safe_blackboard_get("roam_target", Vector2.ZERO)
        
        if movement:
            var entity = get_entity()
            var distance = entity.global_position.distance_to(roam_target)
            if distance < 20.0:
                safe_blackboard_set("roam_target", _get_random_roam_position())
            else:
                movement.move_to(roam_target)
        
        return 2  # RUNNING
    
    func _find_nearby_player():
        # 查找附近玩家的逻辑
        return null
    
    func _get_random_roam_position() -> Vector2:
        # 使用get_entity()获取Entity实例进行位置计算
        var entity = get_entity()
        if entity:
            # 基于Entity当前位置生成随机游荡点
            var current_pos = entity.global_position
            var random_offset = Vector2(randf() * 200 - 100, randf() * 200 - 100)
            return current_pos + random_offset
        return Vector2.ZERO
```

### 状态注册和使用

```gdscript
# 在Entity中创建和使用状态
func setup_guard_ai(entity: Entity):
    var behavior_component = preload("res://BehaviorComponent.tscn").instantiate()
    entity.add_child(behavior_component)
    
    # 创建状态实例
    var idle_state = GuardIdleState.new()
    var patrol_state = GuardPatrolState.new()
    var investigate_state = GuardInvestigateState.new()
    
    # 设置状态列表和初始状态
    behavior_component.states = [idle_state, patrol_state, investigate_state]
    behavior_component.initial_state = "guard_idle"
    
    # 状态机会在_ready()中自动加载配置
```

### 状态配置示例

```gdscript
# 在编辑器中直接配置BehaviorComponent的states数组
# 1. 创建状态实例
var idle_state = GuardIdleState.new()
var patrol_state = GuardPatrolState.new()
var investigate_state = GuardInvestigateState.new()

# 2. 设置到BehaviorComponent的states数组中
behavior_component.states = [idle_state, patrol_state, investigate_state]
behavior_component.initial_state = "guard_idle"
```



### 自定义行为节点

```gdscript
# 切换到指定状态
class_name ChangeStateAction
extends ActionLeaf

@export var target_state_name: String
    
    func tick(actor: Node, blackboard: Blackboard) -> int:
        var behavior_component = blackboard.get_value("behavior_component")
        
        if behavior_component:
            var target_state = behavior_component.state_instances.get(target_state_name)
            if target_state:
                var result = behavior_component.change_state(target_state)
                return 1 if result else 0  # SUCCESS if result else FAILURE
        return 0  # FAILURE

# 执行当前状态
class_name ExecuteCurrentStateAction
extends ActionLeaf

func tick(actor: Node, blackboard: Blackboard) -> int:
        var behavior_component = blackboard.get_value("behavior_component")
        
        if behavior_component:
            return behavior_component.update_current_state()
        return 0  # FAILURE

# 状态检查条件节点
class_name IsInStateCondition
extends ConditionLeaf

@export var expected_state: String
    
    func tick(actor: Node, blackboard: Blackboard) -> int:
        var behavior_component = blackboard.get_value("behavior_component")
        
        if behavior_component and behavior_component.is_in_state(expected_state):
            return 1  # SUCCESS
        return 0  # FAILURE
```

## Data Models

### Blackboard 数据结构

```gdscript
# AI 参数
external_blackboard.set_value("ai_parameters", {
    "detection_range": 100.0,
    "attack_range": 50.0,
    "movement_speed": 80.0,
    "patrol_radius": 200.0
})

# 当前状态数据
external_blackboard.set_value("current_target", null)
external_blackboard.set_value("patrol_points", [])
external_blackboard.set_value("current_patrol_index", 0)

# 组件引用 (在BehaviorComponent初始化时自动设置)
external_blackboard.set_value("behavior_component", behavior_component_instance)
external_blackboard.set_value("entity", entity_instance)
```

## Error Handling

### 错误处理策略

1. **组件缺失处理**
   ```gdscript
   func get_component(component_name: String):
       if actor:
           if actor.has_method("get_component"):
               var component = actor.get_component(component_name)
               if not component:
                   push_warning("Component not found: " + component_name + " on entity: " + str(actor.name))
               return component
           elif actor.has_method("coComponents") and actor.coComponents:
               var component = actor.coComponents.get(component_name)
               if not component:
                   push_warning("Component not found: " + component_name + " on entity: " + str(actor.name))
               return component
       return null
   ```

2. **状态注册错误处理**
    ```gdscript
    func register_state_instance(state_instance: State) -> void:
        if not state_instance:
            push_error("Cannot register null state instance")
            return
        
        state_instances[state_instance.state_name] = state_instance
        state_instance.behavior_component = self
    ```



## Testing Strategy

### 单元测试

```gdscript
func test_behavior_component_state_management():
    var mock_entity = create_mock_entity()
    var behavior_component = preload("res://BehaviorComponent.tscn").instantiate()
    mock_entity.add_child(behavior_component)
    
    # 创建测试状态
    var test_state = TestState.new("test_state")
    
    # 设置状态列表并测试
    behavior_component.states = [test_state]
    behavior_component.initial_state = "test_state"
    behavior_component.load_states_from_config()
    
    assert(behavior_component.state_instances.has("test_state"))
    assert(behavior_component.get_current_state_name() == "test_state")
```

### 集成测试

```gdscript
func test_state_integration():
    var entity = Entity.new()
    var behavior_component = preload("res://BehaviorComponent.tscn").instantiate()
    entity.add_child(behavior_component)
    
    # 创建测试状态
    var guard_state = GuardIdleState.new()
    
    # 设置状态列表并测试
    behavior_component.states = [guard_state]
    behavior_component.initial_state = "guard_idle"
    behavior_component.load_states_from_config()
    
    assert(behavior_component.get_current_state_name() == "guard_idle")
```

## Implementation Notes

### 关键设计决策

1. **集成状态机到BehaviorComponent**
   - 将状态机逻辑直接集成到BehaviorComponent中
   - 基于State基类的enter/update/exit方法管理状态生命周期

2. **直接状态注册机制**
   - 直接创建状态实例并注册到BehaviorComponent
   - 简化的状态管理，无需全局注册表

3. **灵活的组件访问**
   - 通过getComponent方法动态获取组件
   - 支持不同状态使用不同的组件组合

4. **统一的黑板访问**
   - 所有黑板操作都通过BehaviorComponent的公共方法
   - State基类委托给behavior_component处理黑板操作

### 扩展性考虑

- **动态状态添加**: 支持运行时添加新的状态实例
- **状态生命周期管理**: 基于State基类的enter/update/exit方法
- **调试支持**: 继承BeehaveTree的调试功能

### 性能优化

- **减少对象层次**: 移除独立的状态机对象
- **直接集成优化**: 避免额外的委托调用
- **可选blackboard**: 按需配置外部blackboard