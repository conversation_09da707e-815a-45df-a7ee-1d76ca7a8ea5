[gd_resource type="Resource" script_class="SnakeAIConfig" load_steps=2 format=3 uid="uid://bv5l4m1k8pdo8"]

[ext_resource type="Script" uid="uid://qw7vubakxi4m" path="res://Entities/Characters/Snake/SnakeAIConfig.gd" id="1_x5m9k"]

[resource]
resource_local_to_scene = false
resource_name = ""
script = ExtResource("1_x5m9k")
patrol_points = Array[Vector2]([Vector2(-100, 0), Vector2(100, 0), Vector2(0, -100), Vector2(0, 100)])
patrol_speed = 100.0
patrol_wait_time = 2.0
patrol_point_tolerance = 10.0
detection_range = 500.0
enable_line_of_sight = false
sight_ray_count = 3
detection_update_interval = 0.1
chase_speed = 150.0
chase_range = 800.0
min_chase_distance = 50.0
attack_range = 300.0
attack_cooldown = 0.5
aim_time = 0.1
enable_predictive_shooting = true
prediction_factor = 0.5
lost_target_timeout = 3.0
return_to_nearest_patrol_point = true
search_time = 2.0
show_detection_range = false
show_attack_range = false
show_patrol_path = false
enable_debug_logs = true
