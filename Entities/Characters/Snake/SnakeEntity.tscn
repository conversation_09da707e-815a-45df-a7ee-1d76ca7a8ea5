[gd_scene load_steps=15 format=3 uid="uid://lmear3epnvfd"]

[ext_resource type="SpriteFrames" uid="uid://bjrfgre0q8qdf" path="res://Entities/Characters/Snake/SnakeEntityAnimation.tres" id="2_hm77x"]
[ext_resource type="PackedScene" uid="uid://cb8j2kmam4nqp" path="res://Entities/Characters/Snake/CircularMotionSnakeBulletEntity.tscn" id="3_ep0w0"]
[ext_resource type="PackedScene" uid="uid://dekgkos84sljm" path="res://Components/Visual/CameraComponent.tscn" id="3_u3icd"]
[ext_resource type="Script" uid="uid://bgkahipm2d68t" path="res://Resources/EntityConfig.gd" id="5_ep0w0"]
[ext_resource type="PackedScene" uid="uid://ctlquc31j3uwi" path="res://Components/Physics/OverheadPhysicsComponent.tscn" id="5_kofr3"]
[ext_resource type="PackedScene" uid="uid://dn7cpj4u7w1jy" path="res://Components/Physics/CharacterBodyComponent.tscn" id="6_kofr3"]
[ext_resource type="PackedScene" uid="uid://caaxcyrqs036h" path="res://Components/Control/OverheadControlComponent8Direction.tscn" id="6_oiepa"]
[ext_resource type="PackedScene" uid="uid://brb86lryxqbxy" path="res://Components/Movement/Animation8DirectionComponent.tscn" id="7_oiepa"]
[ext_resource type="PackedScene" uid="uid://dsvl41e6hdt58" path="res://Components/Combat/FireComponent.tscn" id="8_xye76"]
[ext_resource type="PackedScene" uid="uid://qn8n4bqswnlr" path="res://Components/Control/MouseRotationComponent.tscn" id="10_253qj"]
[ext_resource type="PackedScene" uid="uid://5tvqunkoa4l7" path="res://Components/Combat/FactionComponent.tscn" id="12_61lko"]

[sub_resource type="GDScript" id="GDScript_ep0w0"]
script/source = "extends Entity
class_name SnakeEntity


func _enter_tree() -> void:
	GameState.addPlayer(self)
	
	
func _exit_tree() -> void:
	GameState.removePlayer(self)
"

[sub_resource type="CircleShape2D" id="CircleShape2D_y2cdj"]

[sub_resource type="Resource" id="Resource_ep0w0"]
script = ExtResource("5_ep0w0")
properties = {
"style": 1
}
description = ""
metadata/_custom_type_script = "uid://bgkahipm2d68t"

[node name="SnakeEntity" type="CharacterBody2D" groups=["players"]]
collision_layer = 16386
collision_mask = 16464
script = SubResource("GDScript_ep0w0")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_y2cdj")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = ExtResource("2_hm77x")
animation = &"attack_down"

[node name="FireComponent" parent="." instance=ExtResource("8_xye76")]
rotation = 1.57118
fireMode = 1
bulletEntity = ExtResource("3_ep0w0")
bulletConfig = SubResource("Resource_ep0w0")
enablePressFire = true

[node name="FactionComponent" parent="." instance=ExtResource("12_61lko")]
factions = 2

[node name="CameraComponent" parent="." instance=ExtResource("3_u3icd")]

[node name="MouseRotationComponent" parent="." node_paths=PackedStringArray("nodeToRotate") instance=ExtResource("10_253qj")]
nodeToRotate = NodePath("../FireComponent")

[node name="CharacterBodyComponent" parent="." instance=ExtResource("6_kofr3")]

[node name="OverheadPhysicsComponent" parent="." instance=ExtResource("5_kofr3")]

[node name="OverheadControlComponent8Direction" parent="." instance=ExtResource("6_oiepa")]
debugMode = false

[node name="Animation8DirectionComponent" parent="." instance=ExtResource("7_oiepa")]
