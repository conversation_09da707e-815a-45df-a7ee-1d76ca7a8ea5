## 实现从原点出发的 S 型向前运动
## 使用正弦函数在前进方向上添加左右摆动，形成 S 型轨迹
## 适用于子弹、导弹等需要 S 型运动轨迹的实体

class_name SinMotionComponent
extends Component


#region Parameters

## 基础前进速度（像素/秒）
@export_range(50, 1000, 10) var forwardSpeed: float = 200

## S 型摆动的振幅（像素）
@export_range(10, 200, 5) var amplitude: float = 50

## S 型摆动的频率（Hz）
@export_range(0.1, 5.0, 0.1) var frequency: float = 1.0

## 是否相对于实体的旋转方向
@export var relativeToRotation: bool = true

## 自定义前进方向（当 relativeToRotation 为 false 时使用）
@export var customDirection: Vector2 = Vector2.RIGHT

## 是否启用组件
@export var isEnabled: bool = true:
	set(newValue):
		isEnabled = newValue
		self.set_physics_process(isEnabled)

## 最大移动距离（0 表示无限制）
@export_range(0, 2000, 50) var maxDistance: float = 0

## 达到最大距离时是否删除实体
@export var deleteAtMaxDistance: bool = false

#endregion


#region State
var time: float = 0.0
var distanceTraveled: float = 0.0
var startPosition: Vector2
var isMoving: bool = true
#endregion


#region Signals
signal reachedMaxDistance
signal position_changed(new_position: Vector2)
#endregion


func _ready() -> void:
	startPosition = parentEntity.global_position
	self.set_physics_process(isEnabled)


func _physics_process(delta: float) -> void:
	if not isEnabled or not isMoving:
		return
	
	# 检查最大距离限制
	if maxDistance > 0 and distanceTraveled >= maxDistance:
		if deleteAtMaxDistance:
			parentEntity.queue_free()
		else:
			isMoving = false
		reachedMaxDistance.emit()
		return
	
	time += delta
	
	# 计算前进方向
	var forwardDirection: Vector2
	if relativeToRotation:
		forwardDirection = Vector2.RIGHT.rotated(parentEntity.rotation)
	else:
		forwardDirection = customDirection.normalized()
	
	# 计算垂直方向（垂直于前进方向）
	var perpendicularDirection: Vector2 = forwardDirection.rotated(PI/2)
	
	# 计算前进距离
	var forwardDistance: float = forwardSpeed * delta
	
	# 计算当前时间点的正弦偏移
	var currentSinOffset: float = sin(time * frequency * 2 * PI) * amplitude
	
	# 计算下一时间点的正弦偏移
	var nextTime: float = time + delta
	var nextSinOffset: float = sin(nextTime * frequency * 2 * PI) * amplitude
	
	# 计算侧向移动距离（正弦偏移的变化量）
	var sideDistance: float = nextSinOffset - currentSinOffset
	
	# 计算总移动向量
	var forwardMovement: Vector2 = forwardDirection * forwardDistance
	var sideMovement: Vector2 = perpendicularDirection * sideDistance
	
	var totalMovement: Vector2 = forwardMovement + sideMovement
	
	# 移动实体
	parentEntity.global_position += totalMovement
	parentEntity.reset_physics_interpolation()
	
	# 广播位置变化
	position_changed.emit(parentEntity.global_position)
	
	# 更新已移动距离
	distanceTraveled += forwardMovement.length()
	
	if debugMode:
		print("SinMotion: forward=", forwardMovement, " side=", sideMovement, " total=", totalMovement, " distance=", distanceTraveled)


## 重置组件状态
func reset() -> void:
	time = 0.0
	distanceTraveled = 0.0
	startPosition = parentEntity.global_position
	isMoving = true


## 设置前进方向
func setDirection(direction: Vector2) -> void:
	customDirection = direction.normalized()


## 设置前进速度
func setSpeed(speed: float) -> void:
	forwardSpeed = speed


## 设置 S 型参数
func setSinParameters(amp: float, freq: float) -> void:
	amplitude = amp
	frequency = freq
