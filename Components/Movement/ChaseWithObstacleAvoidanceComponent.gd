## 基于 `ChaseComponent` 的碰撞式局部避障实现。
## 保留原有“朝目标追踪”的行为，在前方受阻时通过射线探测并按“右手法/左手法”进行贴边转向。
##
## 注意：
## - 不修改 `ChaseComponent.gd`，本脚本是其子类。
## - `collisionMaskForWalls` 需与关卡中墙体/障碍的碰撞层一致。
## - 本组件只输出方向，实际移动仍由 `OverheadPhysicsComponent` 负责。

class_name ChaseWithObstacleAvoidanceComponent
extends ChaseComponent


#region 参数
## 墙体/障碍物的碰撞掩码；务必与关卡设置保持一致。
@export var collisionMaskForWalls: int = 1

## 探针射线长度（用于探测局部障碍）。
@export var probeLength: float = 64.0

## 侧向探针相对期望追踪方向的角度（度）。
@export var probeSideAngleDegrees: float = 35.0

## 遇阻时的转向角度（度）。
@export var steerTurnAngleDegrees: float = 60.0

## 是否优先使用右手法贴边；为 false 则使用左手法。
@export var preferRightHandFollow: bool = true

## 方向保持的冷却帧数（抑制抖动）。
@export var steerCooldownFrames: int = 6

## 贴边保持的帧数（在贴墙行走期间锁定左右侧，避免频繁换边）。
@export var wallFollowHoldFrames: int = 18

## 判定“卡住”的最小位移（像素）。
@export var stuckDistanceThreshold: float = 4.0

## 判定“卡住”的时间阈值（秒）。
@export var stuckTimeSec: float = 0.5

## 是否启用“最大净空”导航模式（无视线时，朝向探测到的最远可行方向）。
@export var useMaxClearanceWhenNoLOS: bool = true

## 最大净空模式：射线数量（越多越精细，但更耗性能）。
@export var scanRayCount: int = 24

## 最大净空模式：射线长度（像素）。
@export var scanRayLength: float = 240.0

## 最大净空模式：仅考虑前方半平面角度（度，建议 120~180）。
@export var forwardHalfAngleDegrees: float = 160.0

## 重新评估（重扫描）的最小位移阈值（像素）。
@export var rescanStepDistance: float = 20.0

## 将“前方半平面”的参考方向改为“最近一次真实行走方向”（速度向量优先），避免因朝向或找玩家瞬时转头导致扫描基准跳变。
@export var useVelocityForForwardDir: bool = true
@export var minSpeedForForwardDir: float = 10.0

## 目标方向偏好权重（越大越偏向玩家方向）。
@export var targetBiasWeight: float = 0.2

## 卡住时允许扩大为近360°扫描，避免始终只看“前半面”。
@export var allowFullScanWhenStuck: bool = true
@export var fullScanAngleDegrees: float = 340.0
@export var fullScanStuckTimeSec: float = 1.0
#endregion


#region 状态
var _cooldownFramesRemaining: int = 0
var _steerDirection: Vector2 = Vector2.ZERO
var _wallFollowFramesRemaining: int = 0
var _wallFollowSide: int = 0 # 1=右手法，-1=左手法，0=未贴边
var _stuckTimer: float = 0.0
var _lastPos: Vector2 = Vector2.ZERO
var _distanceSinceScan: float = 0.0
var _clearanceDirection: Vector2 = Vector2.ZERO
#endregion


func _physics_process(_delta: float) -> void:
	# 基本条件：启用 + 有实体 + 有目标
	if not isEnabled or not self.body or not nodeToChase:
		return

	var from: Vector2 = parentEntity.global_position
	var to: Vector2 = nodeToChase.global_position
	var desiredDir: Vector2 = (to - from).normalized()

	# 更新“卡住”计时器
	if _lastPos == Vector2.ZERO:
		_lastPos = from
	var moved := from.distance_to(_lastPos)
	if moved > stuckDistanceThreshold:
		_stuckTimer = 0.0
		_lastPos = from
	else:
		_stuckTimer += _delta

	# 1) 若对目标具备直线视线（LOS），则直接朝向目标移动。
	if _has_line_of_sight(from, to):
		_steerDirection = desiredDir
		_cooldownFramesRemaining = 0
		_wallFollowFramesRemaining = 0
		_wallFollowSide = 0
		_distanceSinceScan = 0.0
		_clearanceDirection = Vector2.ZERO
		recentChaseDirection = _steerDirection
		overheadPhysicsComponent.inputDirection = _steerDirection
		if debugMode: showDebugInfo()
		return

	# 2) 无 LOS 时：若启用“最大净空”模式，则优先使用该模式；否则使用贴边法。
	if useMaxClearanceWhenNoLOS:
		_distanceSinceScan += moved
		var forwardDir := _compute_forward_dir(desiredDir)
		if _distanceSinceScan >= rescanStepDistance or _clearanceDirection == Vector2.ZERO:
			_clearanceDirection = _scan_max_clearance_direction(from, forwardDir, desiredDir)
			_distanceSinceScan = 0.0

		var steerDirClearance := _clearanceDirection
		if steerDirClearance == Vector2.ZERO:
			steerDirClearance = forwardDir

		_steerDirection = steerDirClearance.normalized()
		recentChaseDirection = _steerDirection
		overheadPhysicsComponent.inputDirection = _steerDirection
		if debugMode: showDebugInfo()
		return

	# 3) 回退：发射前/左前/右前三根探针，并按右/左手法进行贴边转向。
	var dirForward := desiredDir
	var dirRight := desiredDir.rotated(deg_to_rad(-probeSideAngleDegrees))
	var dirLeft := desiredDir.rotated(deg_to_rad(+probeSideAngleDegrees))

	var hitForward := _raycast(from, dirForward, probeLength)
	var hitRight := _raycast(from, dirRight, probeLength)
	var hitLeft := _raycast(from, dirLeft, probeLength)

	var steerDir := desiredDir
	if not hitForward.is_empty():
		# 锁定贴边侧：若未锁定则按偏好或更空的一侧决定
		if _wallFollowFramesRemaining <= 0 or _wallFollowSide == 0:
			var rightBlocked := not hitRight.is_empty()
			var leftBlocked := not hitLeft.is_empty()
			if not rightBlocked and preferRightHandFollow:
				_wallFollowSide = 1
			elif not leftBlocked and not preferRightHandFollow:
				_wallFollowSide = -1
			else:
				# 比较两侧“空旷程度”：未命中的一侧优先；若都命中，用命中距离更远的一侧
				var distRight := INF if hitRight.is_empty() else from.distance_to(hitRight.position)
				var distLeft := INF if hitLeft.is_empty() else from.distance_to(hitLeft.position)
				_wallFollowSide = 1 if distRight >= distLeft else -1
			_wallFollowFramesRemaining = wallFollowHoldFrames
		else:
			_wallFollowFramesRemaining -= 1

		# 使用碰撞法线的切向作为行进方向，确保沿墙滑行而非来回横跳
		var n: Vector2 = hitForward.normal
		var tangent := Vector2(-n.y, n.x)  # 右手切向
		tangent = tangent if _wallFollowSide == 1 else -tangent
		if tangent.dot(desiredDir) < 0:
			tangent = -tangent
		steerDir = tangent.normalized()

	# 3) 抖动抑制：遇阻时保留上一帧的转向若干帧。
	if not hitForward.is_empty() or not hitLeft.is_empty() or not hitRight.is_empty():
		if _cooldownFramesRemaining > 0:
			_cooldownFramesRemaining -= 1
			if _steerDirection != Vector2.ZERO:
				steerDir = _steerDirection
		else:
			_cooldownFramesRemaining = steerCooldownFrames
	else:
		_cooldownFramesRemaining = 0

	# 简单脱困：若长时间位移很小，则切换贴边侧
	if _stuckTimer >= stuckTimeSec:
		_wallFollowSide = -_wallFollowSide if _wallFollowSide != 0 else (_wallFollowSide if preferRightHandFollow else -1)
		_wallFollowFramesRemaining = wallFollowHoldFrames
		_stuckTimer = 0.0

	_steerDirection = steerDir.normalized()
	recentChaseDirection = _steerDirection
	overheadPhysicsComponent.inputDirection = _steerDirection

	if debugMode: showDebugInfo()


func _has_line_of_sight(from: Vector2, to: Vector2) -> bool:
	var space_state = get_viewport().get_world_2d().direct_space_state
	var params := PhysicsRayQueryParameters2D.create(from, to)
	params.collision_mask = collisionMaskForWalls
	params.hit_from_inside = true
	var hit := space_state.intersect_ray(params)
	return hit.is_empty()


func _raycast(origin: Vector2, direction: Vector2, distance: float) -> Dictionary:
	var space_state = get_viewport().get_world_2d().direct_space_state
	var params := PhysicsRayQueryParameters2D.create(origin, origin + direction.normalized() * distance)
	params.collision_mask = collisionMaskForWalls
	params.hit_from_inside = true
	return space_state.intersect_ray(params)


## 扫描前方半平面，选择“到首次碰撞距离最大”的方向。
func _scan_max_clearance_direction(origin: Vector2, forwardDir: Vector2, biasToTargetDir: Vector2) -> Vector2:
	var rays: int = max(3, scanRayCount)
	var scanAngleDeg: float = forwardHalfAngleDegrees
	if allowFullScanWhenStuck and _stuckTimer >= fullScanStuckTimeSec:
		scanAngleDeg = clamp(fullScanAngleDegrees, 10.0, 359.0)
	var scanRad: float = deg_to_rad(scanAngleDeg)
	var startAngle: float = -scanRad * 0.5
	var step: float = scanRad / float(rays - 1)

	var bestDir := Vector2.ZERO
	var bestDist := -1.0

	for i in rays:
		var angle: float = startAngle + step * float(i)
		var dir: Vector2 = forwardDir.rotated(angle).normalized()
		# 若扫描角度小于180°，仅考虑前方半平面
		if scanAngleDeg < 180.0 and dir.dot(forwardDir) <= 0.0:
			continue
		var hit: Dictionary = _raycast(origin, dir, scanRayLength)
		var dist: float = scanRayLength
		if not hit.is_empty():
			var hit_pos: Vector2 = hit.get("position", origin)
			dist = origin.distance_to(hit_pos)
		# 采用轻微的目标方向偏好，减少“走偏”：
		var bias: float = max(0.0, dir.dot(biasToTargetDir)) # 0~1
		var score: float = dist * (1.0 + targetBiasWeight * bias)
		if score > bestDist:
			bestDist = score
			bestDir = dir

	return bestDir


## 计算前进参考方向（用于限定“只看前半平面”）：
## 优先使用角色当前速度方向（如果足够大），否则使用上一帧的行走方向，再否则退化为本帧的理想追踪方向。
func _compute_forward_dir(fallbackDesiredDir: Vector2) -> Vector2:
	if useVelocityForForwardDir and self.body:
		var v: Vector2 = self.body.velocity
		if v.length() >= minSpeedForForwardDir:
			return v.normalized()
	if recentChaseDirection != Vector2.ZERO:
		return recentChaseDirection.normalized()
	return fallbackDesiredDir.normalized()
