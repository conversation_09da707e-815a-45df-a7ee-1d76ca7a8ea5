## 节点跟随组件
## 通过指定节点，实现跟随功能
## 适用于编队飞行、跟随移动等场景

class_name NodeFollowComponent
extends Component

#region Parameters

## 要跟随的目标节点
@export var target_node: Node2D

## 跟随距离（与目标的距离）
@export_range(0.0, 1000.0, 1.0) var follow_distance: float = 50.0

## 跟随速度
@export_range(0.0, 1000.0, 10.0) var follow_speed: float = 100.0

## 是否启用跟随
@export var is_enabled: bool = true

## 是否平滑跟随（使用插值）
@export var smooth_follow: bool = true

## 平滑跟随的插值权重（0-1）
@export_range(0.0, 1.0, 0.01) var smooth_weight: float = 0.1

## 是否保持相对角度
@export var maintain_relative_angle: bool = false

## 相对角度偏移（弧度）
@export_range(-3.14159, 3.14159, 0.1) var angle_offset: float = 0.0

#endregion

#region Dependencies
var target_position: Vector2
var current_velocity: Vector2
#endregion

#region Signals
signal target_lost
signal target_found
signal following_started
signal following_stopped
#endregion

#region Initialization

func _ready() -> void:
	if target_node:
		target_found.emit()
		following_started.emit()

func _physics_process(delta: float) -> void:
	if not is_enabled or not target_node or not is_instance_valid(target_node):
		if target_node and not is_instance_valid(target_node):
			target_lost.emit()
			target_node = null
		return
	
	update_follow_position(delta)

#endregion

#region Core Methods

## 设置跟随目标
func set_target(new_target: Node2D) -> void:
	var old_target = target_node
	target_node = new_target
	
	if old_target != new_target:
		if new_target:
			target_found.emit()
			following_started.emit()
		else:
			target_lost.emit()
			following_stopped.emit()

## 更新跟随位置
func update_follow_position(delta: float) -> void:
	if not target_node or not is_instance_valid(target_node):
		return
	
	# 计算目标位置
	var target_global_pos = target_node.global_position
	var direction_to_target = (parentEntity.global_position - target_global_pos).normalized()
	
	# 计算理想的跟随位置
	var ideal_follow_position: Vector2
	
	if maintain_relative_angle:
		# 保持相对角度模式
		var target_angle = target_node.rotation + angle_offset
		var follow_direction = Vector2(cos(target_angle), sin(target_angle))
		ideal_follow_position = target_global_pos + follow_direction * follow_distance
	else:
		# 距离跟随模式
		ideal_follow_position = target_global_pos + direction_to_target * follow_distance
	
	# 计算移动
	var current_pos = parentEntity.global_position
	var target_velocity: Vector2
	
	if smooth_follow:
		# 平滑跟随
		target_velocity = (ideal_follow_position - current_pos) * smooth_weight
		current_velocity = current_velocity.lerp(target_velocity, smooth_weight)
		parentEntity.global_position += current_velocity
	else:
		# 直接跟随
		var move_direction = (ideal_follow_position - current_pos).normalized()
		var move_distance = follow_speed * delta
		var distance_to_target = current_pos.distance_to(ideal_follow_position)
		
		if distance_to_target > move_distance:
			parentEntity.global_position += move_direction * move_distance
		else:
			parentEntity.global_position = ideal_follow_position

## 获取当前跟随状态
func get_follow_status() -> Dictionary:
	return {
		"is_following": is_enabled and target_node != null and is_instance_valid(target_node),
		"target_position": target_node.global_position if target_node else Vector2.ZERO,
		"current_position": parentEntity.global_position,
		"distance_to_target": parentEntity.global_position.distance_to(target_node.global_position) if target_node else 0.0
	}

## 设置跟随距离
func set_follow_distance(distance: float) -> void:
	follow_distance = max(0.0, distance)

## 设置跟随速度
func set_follow_speed(speed: float) -> void:
	follow_speed = max(0.0, speed)

## 启用/禁用跟随
func set_enabled(enabled: bool) -> void:
	is_enabled = enabled
	if not enabled:
		following_stopped.emit()

#endregion

#region Utility Methods

## 检查是否正在跟随
func is_following() -> bool:
	return is_enabled and target_node != null and is_instance_valid(target_node)

## 获取到目标的距离
func get_distance_to_target() -> float:
	if not target_node or not is_instance_valid(target_node):
		return 0.0
	return parentEntity.global_position.distance_to(target_node.global_position)

## 获取到目标的方向
func get_direction_to_target() -> Vector2:
	if not target_node or not is_instance_valid(target_node):
		return Vector2.ZERO
	return (target_node.global_position - parentEntity.global_position).normalized()

#endregion
