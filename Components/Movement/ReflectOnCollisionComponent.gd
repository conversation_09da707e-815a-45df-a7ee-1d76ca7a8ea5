class_name ReflectOnCollisionComponent
extends Component

## 最大反弹次数，超过后销毁实体
@export_range(0, 10, 1) var maxBounceCount: int = 3

## 是否启用反弹
@export var isEnabled: bool = true

# 内部状态
var bounceCount: int = 0

func _physics_process(delta: float) -> void:
	if not isEnabled:
		return
	
	var body = parentEntity.getBody()
	if not body:
		return
	
	# 获取当前移动方向（基于实体的旋转）
	var direction = Vector2.RIGHT.rotated(parentEntity.rotation)
	
	# 尝试移动并检测碰撞（使用一个小的移动距离来检测）
	var collision = body.move_and_collide(direction * 1.0, true)
	
	if collision:
		# 计算反弹方向
		var bounceDirection = direction.bounce(collision.get_normal())
		
		# 更新实体的旋转角度以匹配新的运动方向
		parentEntity.rotation = bounceDirection.angle()
		
		# 增加反弹计数
		bounceCount += 1
		
		# 检查是否超过最大反弹次数
		if maxBounceCount > 0 and bounceCount >= maxBounceCount:
			if debugMode:
				printDebug("达到最大反弹次数 (" + str(maxBounceCount) + ")，销毁实体")
			parentEntity.queue_free()
		else:
			if debugMode:
				printDebug("反弹 #" + str(bounceCount) + " 发生，新方向: " + str(rad_to_deg(bounceDirection.angle())) + "°")
