## Moves the entity along an elliptical trajectory with configurable forward motion and elliptical parameters.
## Uses parametric equations: x = centerX + radiusX * cos(angle), y = centerY + radiusY * sin(angle)
## Supports both forward movement and simultaneous elliptical motion for boomerang-style projectiles.

class_name EllipticalMotionComponent
extends Component

#region Parameters

@export_range(0.1, 2000.0, 0.1) var forwardSpeed: float = 200.0  ## Speed of forward motion along the trajectory
@export_range(0.1, 500.0, 0.1) var ellipticalRadiusX: float = 50.0  ## Horizontal radius of the ellipse
@export_range(0.1, 500.0, 0.1) var ellipticalRadiusY: float = 30.0  ## Vertical radius of the ellipse
@export_range(0.1, 20.0, 0.1) var ellipticalSpeed: float = 5.0  ## Speed of elliptical motion (radians per second)

@export var shouldMoveForward: bool = true  ## Enable forward motion
@export var shouldApplyEllipticalMotion: bool = true  ## Enable elliptical motion
@export var isEnabled: bool = true  ## Master enable/disable flag

#endregion

#region State

var centerPosition: Vector2  ## Center point of the elliptical motion
var currentAngle: float = 0.0  ## Current angle in the elliptical path (radians)
var previousPosition: Vector2  ## Previous frame position for delta calculations

# Cached values for performance optimization
var _cos_cache: float = 1.0
var _sin_cache: float = 0.0
var _last_milestone_angle: int = -1  ## Track last milestone to avoid duplicate signals
var _cycle_count: int = 0  ## Count of complete elliptical cycles

# Performance optimization: Skip calculations if values haven't changed significantly
var _last_angle_update: float = 0.0
var _angle_update_threshold: float = 0.01  ## Minimum angle change to trigger updates (radians)

#endregion

#region Signals

## Emitted when trajectory starts
signal trajectory_started(center: Vector2)

## Emitted at specific angle milestones (every 90 degrees / PI/2 radians)
signal angle_milestone_reached(angle_degrees: float, position: Vector2)

## Emitted when direction changes (based on velocity direction)
signal direction_changed(new_direction: Vector2)

## Emitted when a complete elliptical cycle is finished
signal elliptical_cycle_completed(cycles_completed: int)

#endregion

#region Core Mathematical Functions

## Calculate elliptical position using parametric equations
## x = centerX + radiusX * cos(angle), y = centerY + radiusY * sin(angle)
func calculate_elliptical_position(center: Vector2, radiusX: float, radiusY: float, angle: float) -> Vector2:
	return Vector2(
		center.x + radiusX * cos(angle),
		center.y + radiusY * sin(angle)
	)

## Calculate elliptical offset from center position
func calculate_elliptical_offset(radiusX: float, radiusY: float, angle: float) -> Vector2:
	return Vector2(
		radiusX * cos(angle),
		radiusY * sin(angle)
	)

## Normalize angle to 0-2π range
func normalize_angle(angle: float) -> float:
	while angle < 0:
		angle += TAU  # TAU = 2π in Godot
	while angle >= TAU:
		angle -= TAU
	return angle

## Calculate distance between two points
func calculate_distance(point1: Vector2, point2: Vector2) -> float:
	return point1.distance_to(point2)

## Get elliptical trajectory points for visualization/debugging
func get_elliptical_trajectory_points(center: Vector2, radiusX: float, radiusY: float, point_count: int = 36) -> Array[Vector2]:
	var points: Array[Vector2] = []
	var angle_step: float = TAU / float(point_count)
	
	for i in range(point_count + 1):
		var angle: float = i * angle_step
		var point: Vector2 = calculate_elliptical_position(center, radiusX, radiusY, angle)
		points.append(point)
	
	return points

## Validate elliptical parameters to ensure they are within acceptable ranges
func validate_parameters() -> bool:
	if ellipticalRadiusX <= 0 or ellipticalRadiusY <= 0:
		printWarning("Invalid elliptical radius: radiusX=" + str(ellipticalRadiusX) + ", radiusY=" + str(ellipticalRadiusY))
		return false
	
	if forwardSpeed < 0:
		printWarning("Invalid forward speed: " + str(forwardSpeed))
		return false
	
	if ellipticalSpeed < 0:
		printWarning("Invalid elliptical speed: " + str(ellipticalSpeed))
		return false
	
	return true

#endregion

#region Lifecycle

func _ready() -> void:
	# Validate parameters
	if not validate_parameters():
		printError("Invalid parameters detected. Component disabled.")
		isEnabled = false
		return
	
	# Initialize state
	centerPosition = parentEntity.global_position
	previousPosition = centerPosition
	currentAngle = 0.0
	
	# Cache initial trigonometric values
	_update_trig_cache()
	
	if isEnabled:
		trajectory_started.emit(centerPosition)
		if debugMode:
			printDebug("Trajectory started at center: " + str(centerPosition))

func _physics_process(delta: float) -> void:
	if not isEnabled or not parentEntity:
		return
	
	var angle_changed: bool = false
	
	# Update angle progression with change detection
	if shouldApplyEllipticalMotion:
		var new_angle: float = currentAngle + ellipticalSpeed * delta
		
		# Only update if the change is significant enough
		if abs(new_angle - _last_angle_update) >= _angle_update_threshold:
			currentAngle = normalize_angle(new_angle)
			_last_angle_update = currentAngle
			angle_changed = true
			
			# Check for complete cycle
			if currentAngle < _angle_update_threshold and (_last_angle_update - currentAngle) > PI:
				_cycle_count += 1
				elliptical_cycle_completed.emit(_cycle_count)
		
		# Check for angle milestones only if angle changed significantly
		if angle_changed:
			_check_angle_milestones()
	
	# Calculate new position (optimized)
	var newPosition: Vector2 = _calculate_frame_position_optimized(delta, angle_changed)
	
	# Apply forward motion if enabled
	if shouldMoveForward:
		var forwardDirection: Vector2 = Vector2.RIGHT.rotated(parentEntity.rotation)
		var forwardOffset: Vector2 = forwardDirection * forwardSpeed * delta
		newPosition += forwardOffset
		centerPosition += forwardOffset
	
	# Check for direction changes (less frequent for performance)
	if angle_changed:
		var currentVelocity: Vector2 = (newPosition - previousPosition) / delta
		if currentVelocity.length_squared() > 0.01:  # Use length_squared for better performance
			_check_direction_change(currentVelocity.normalized())
	
	# Update entity position
	parentEntity.global_position = newPosition
	previousPosition = newPosition
	
	if debugMode and angle_changed:
		printDebug("Position: " + str(newPosition) + ", Angle: " + str(rad_to_deg(currentAngle)) + "°")

#endregion

#region Internal Helper Functions

## Calculate position for current frame (original method, kept for compatibility)
func _calculate_frame_position(delta: float) -> Vector2:
	if not shouldApplyEllipticalMotion:
		return parentEntity.global_position
	
	# Use cached trigonometric values for performance
	_update_trig_cache()
	
	var ellipticalOffset: Vector2 = Vector2(
		ellipticalRadiusX * _cos_cache,
		ellipticalRadiusY * _sin_cache
	)
	
	return centerPosition + ellipticalOffset

## Optimized position calculation that reuses cached values when possible
func _calculate_frame_position_optimized(delta: float, force_update: bool = false) -> Vector2:
	if not shouldApplyEllipticalMotion:
		return parentEntity.global_position
	
	# Only update trigonometric cache if angle changed significantly or forced
	if force_update:
		_update_trig_cache()
	
	var ellipticalOffset: Vector2 = Vector2(
		ellipticalRadiusX * _cos_cache,
		ellipticalRadiusY * _sin_cache
	)
	
	return centerPosition + ellipticalOffset

## Update cached trigonometric values (optimized to skip if not needed)
func _update_trig_cache() -> void:
	_cos_cache = cos(currentAngle)
	_sin_cache = sin(currentAngle)

## Check for angle milestones and emit signals (optimized to avoid duplicate signals)
func _check_angle_milestones() -> void:
	var degrees: float = rad_to_deg(currentAngle)
	var milestone: int = int(degrees / 90.0) % 4  # 0, 1, 2, 3 for 0°, 90°, 180°, 270°
	
	# Only emit if this is a new milestone
	if milestone != _last_milestone_angle:
		var milestone_angle: float = milestone * 90.0
		angle_milestone_reached.emit(milestone_angle, parentEntity.global_position)
		_last_milestone_angle = milestone
		
		if debugMode:
			printDebug("Angle milestone reached: " + str(milestone_angle) + "°")

var _last_direction: Vector2 = Vector2.ZERO

## Check for direction changes and emit signals (optimized threshold)
func _check_direction_change(new_direction: Vector2) -> void:
	# Use dot product for more efficient direction comparison
	if _last_direction != Vector2.ZERO:
		var dot_product: float = _last_direction.dot(new_direction)
		if dot_product < 0.9:  # Significant direction change threshold
			direction_changed.emit(new_direction)
			if debugMode:
				printDebug("Direction changed from " + str(_last_direction) + " to " + str(new_direction))
	
	_last_direction = new_direction

#endregion

#region Public Interface

## Set new elliptical parameters at runtime
func set_elliptical_parameters(radiusX: float, radiusY: float, speed: float) -> void:
	ellipticalRadiusX = radiusX
	ellipticalRadiusY = radiusY
	ellipticalSpeed = speed
	
	if not validate_parameters():
		printWarning("Invalid parameters provided to set_elliptical_parameters")

## Set new center position for the elliptical motion
func set_center_position(new_center: Vector2) -> void:
	centerPosition = new_center
	if debugMode:
		printDebug("Center position updated to: " + str(new_center))

## Get current elliptical motion info
func get_motion_info() -> Dictionary:
	return {
		"center_position": centerPosition,
		"current_angle_radians": currentAngle,
		"current_angle_degrees": rad_to_deg(currentAngle),
		"elliptical_radius_x": ellipticalRadiusX,
		"elliptical_radius_y": ellipticalRadiusY,
		"forward_speed": forwardSpeed,
		"elliptical_speed": ellipticalSpeed,
		"is_enabled": isEnabled
	}

## Reset trajectory to starting state
func reset_trajectory() -> void:
	currentAngle = 0.0
	centerPosition = parentEntity.global_position
	previousPosition = centerPosition
	_last_direction = Vector2.ZERO
	_last_milestone_angle = -1
	_cycle_count = 0
	_last_angle_update = 0.0
	_update_trig_cache()
	
	if isEnabled:
		trajectory_started.emit(centerPosition)

#endregion