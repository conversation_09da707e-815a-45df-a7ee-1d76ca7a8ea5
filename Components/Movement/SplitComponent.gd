## 分裂组件 - 在达到最大距离或发生碰撞时分裂实体
## 分裂时会创建指定数量的子实体，以360度平分的方向发射

class_name SplitComponent
extends Component

#region Parameters

## 分裂时创建的子实体场景
@export var splitEntityScene: PackedScene

@export var entityConfig: EntityConfig

## 分裂数量（子实体数量）
@export_range(2, 12, 1) var splitCount: int = 4

## 是否启用距离触发分裂（通过LinearMotionComponent信号）
@export var enableDistanceSplit: bool = false

## 是否启用碰撞触发分裂
@export var enableCollisionSplit: bool = true

## 是否启用组件
@export var isEnabled: bool = true

#endregion

#region State
var hasSplit: bool = false
var linearMotionComponent: LinearMotionComponent
#endregion

#region Signals
signal didSplit(splitEntities: Array[Entity])
#endregion

func _ready() -> void:
	# 如果启用距离分裂，尝试获取LinearMotionComponent
	if enableDistanceSplit:
		linearMotionComponent = coComponents.get(&"LinearMotionComponent")
		linearMotionComponent.didReachMaximumDistance.connect(_on_linear_motion_reached_maximum_distance)

func _physics_process(delta: float) -> void:
	if not isEnabled or hasSplit:
		return
	
	# 检查碰撞触发分裂
	if enableCollisionSplit:
		checkCollisionSplit()

## 处理LinearMotionComponent达到最大距离的信号
func _on_linear_motion_reached_maximum_distance() -> void:
	if not isEnabled or hasSplit:
		return
	
	if enableDistanceSplit:
		performSplit()

## 检查碰撞触发分裂
func checkCollisionSplit() -> void:
	var body = parentEntity.getBody()
	if not body:
		return
	
	var direction = Vector2.RIGHT.rotated(parentEntity.rotation)
	var collision = body.move_and_collide(direction * 1.0, true)
	
	if collision:
		var collider = collision.get_collider()
		if shouldTriggerSplit(collider):
			if debugMode:
				printDebug("检测到TileMapLayer碰撞，触发分裂")
				printDebug("碰撞对象: " + str(collider.name) + " 类型: " + str(collider.get_class()))
			# 使用碰撞位置进行分裂
			performSplit()
		else:
			if debugMode:
				printDebug("检测到碰撞，但对象不触发分裂: " + str(collider.name) + " 类型: " + str(collider.get_class()))

## 判断是否应该触发分裂
func shouldTriggerSplit(collider: Node) -> bool:
	if not collider:
		return false
	
	# 只检测 TileMapLayer
	if collider is TileMapLayer:
		return true
	
	return false

## 执行分裂操作
## splitPosition: 可选的分裂起始位置，如果不提供则使用当前实体位置
func performSplit(splitPosition: Vector2 = parentEntity.global_position) -> void:
	if hasSplit:
		return
		
	if not splitEntityScene:
		return
	
	hasSplit = true
	
	var splitEntities: Array[Entity] = []
	var angleStep = 360.0 / splitCount
	
	# 创建分裂实体
	for i in range(splitCount):
		var childEntity = splitEntityScene.instantiate() as Entity
		if not childEntity:
			printError("SplitComponent: Cannot instantiate split entity")
			continue
		
		if entityConfig:
			entityConfig.applyToEntity(childEntity)
		
		var childAngle = deg_to_rad(i * angleStep)
		
		# 设置分裂实体的位置和方向
		childEntity.global_position = splitPosition
		childEntity.global_rotation = childAngle
		
		# 添加到场景
		var parent = parentEntity.get_parent()
		if parent:
			# 设置 top_level 为 true，使子实体不受父节点变换影响
			childEntity.top_level = true
			parent.add_child(childEntity, false)
			childEntity.owner = parent
			# 设置全局位置
			childEntity.global_position = splitPosition
			splitEntities.append(childEntity)
			
			if debugMode:
				printDebug("创建分裂实体 #" + str(i + 1) + "，方向: " + str(rad_to_deg(childAngle)) + "°，位置: " + str(childEntity.global_position))
		else:
			printError("SplitComponent: Cannot find parent for split entity")
			childEntity.queue_free()
	
	# 发出分裂信号
	didSplit.emit(splitEntities)
	
	# 销毁原实体
	parentEntity.queue_free()

## 获取必需的组件
func getRequiredComponents() -> Array[Script]:
	# 只有当启用距离分裂时才需要LinearMotionComponent
	if enableDistanceSplit:
		return [LinearMotionComponent]
	return []

## 初始化组件
func _enter_tree() -> void:
	super._enter_tree()
