extends Component
class_name Animation8DirectionComponent

enum AnimationState {
	BIRTH,      # 出生状态
	IDLE,       # 待机状态
	MOVE,       # 移动状态
	HURT,       # 受伤状态
	DEATH,      # 死亡状态
	ATTACK,      # 攻击状态
	HIGHATTACK,     # 高优先级攻击状态
	HIDE,       # 隐藏状态
	SHOW        # 显示状态
}

enum Faction {
	PLAYERS,    # 玩家阵营
	ENEMIES,    # 敌人阵营
	BOSS        # BOSS 阵营（受击不打断）
}

const STATE_PRIORITY = {
	AnimationState.BIRTH: 30,    # 最高优先级
	AnimationState.DEATH: 25,    # 最高优先级
	AnimationState.HURT: 20,     # 高优先级
	AnimationState.HIGHATTACK: 15,   # 中优先级
	AnimationState.SHOW: 14,     # 显示状态优先级
	AnimationState.HIDE: 13,     # 隐藏状态优先级
	AnimationState.MOVE: 10,     # 低优先级
	AnimationState.ATTACK: 5,   # 中优先级
	AnimationState.IDLE: 0      # 最低优先级
}

#region Parameters
@export var animatedSprite: AnimatedSprite2D
@export var enableAnimationCache: bool = true
@export var defaultState: AnimationState = AnimationState.IDLE
@export var faction: Faction = Faction.ENEMIES  # 阵营设置，默认为敌人；BOSS阵营受击不打断
# 新增：默认播放速度（全局）
@export var default_speed_scale: float = 1.0
#endregion

#region Signals
signal animationStateChanged(newState: AnimationState, oldState: AnimationState)
signal animationPlayed(animationName: String, direction: Vector2, state: AnimationState)
signal animationCompleted(completedState: AnimationState)
signal animationFrameReached(frameNumber: int, animationName: String, state: AnimationState)  # 新增：动画播放到指定帧时发射
signal hurtAnimationStarted()  # 新增：受伤动画开始播放时发射
#endregion

#region State
var currentState: AnimationState = AnimationState.IDLE
var requestedStates: Dictionary = {}
var lastPlayedAnimation: String = ""
var lastFlipState: bool = false
var currentDirection: Vector2 = Vector2.DOWN

var targetFrameNumber: int = 20  # 新增：目标帧数
var frameReachedSignaled: bool = false  # 新增：是否已经发射过帧信号
var targetFrameNumber2: int = -1  # 新增：目标帧数
var frameReachedSignaled2: bool = false  # 新增：是否已经发射过帧信号
var concernState: AnimationState = AnimationState.ATTACK

# 新增：下一次播放速度（仅对下一次play生效，播放后重置为未设置）
var next_play_speed_scale: float = -1.0
#endregion


func _ready() -> void:
	initializeAnimatedSprite()
	connectSignals()
	checkRequiredComponents()
	
	# 检查 AnimatedSprite2D 的 autoplay 设置
	if animatedSprite and animatedSprite.autoplay == "born":
		currentState = AnimationState.BIRTH
		printDebug("[Animation8DirectionComponent] Animation8DirectionComponent: 检测到 autoplay='born'，设置初始状态为 BIRTH")
		
		# 临时禁用动画缓存，确保信号能正常发射
		var originalCacheSetting = enableAnimationCache
		enableAnimationCache = false
		
		# 如果 AnimatedSprite2D 正在播放 born 动画，发射信号
		if animatedSprite.animation == "born":
			printDebug("[Animation8DirectionComponent] Animation8DirectionComponent: 检测到正在播放 born 动画，发射信号")
			animationPlayed.emit("born", Vector2.DOWN, AnimationState.BIRTH)
		
		# 恢复动画缓存设置
		enableAnimationCache = originalCacheSetting
	else:
		currentState = defaultState
	
	if currentState == AnimationState.BIRTH:
		playBirthAnimation()
	else:
		playIdleAnimation(currentDirection)


func initializeAnimatedSprite() -> void:
	if not animatedSprite and parentEntity:
		animatedSprite = parentEntity.get_node_or_null("AnimatedSprite2D")
		if not animatedSprite:
			for child in parentEntity.get_children():
				if child is AnimatedSprite2D:
					animatedSprite = child
					break
	# 应用默认速度
	if animatedSprite:
		animatedSprite.speed_scale = max(default_speed_scale, 0.0)

func connectSignals() -> void:
	# 安全地连接 HealthComponent 信号
	if coComponents.has("HealthComponent"):
		var healthComponent: HealthComponent = coComponents.HealthComponent
		#如果有死亡动画，禁止healthComponent移除实体，在死亡动画完成后移除
		if animatedSprite and hasDeathAnimation():
			healthComponent.shouldRemoveEntityOnZero = false
		if healthComponent.has_signal("healthDidDecrease") and not healthComponent.healthDidDecrease.is_connected(_on_health_decreased):
			healthComponent.healthDidDecrease.connect(_on_health_decreased)
		if healthComponent.has_signal("healthDidZero") and not healthComponent.healthDidZero.is_connected(_on_health_zero):
			healthComponent.healthDidZero.connect(_on_health_zero)

	# 安全地连接 CharacterBodyComponent 信号
	if coComponents.has("CharacterBodyComponent"):
		var bodyComponent: CharacterBodyComponent = coComponents.CharacterBodyComponent
		if bodyComponent.has_signal("didMove") and not bodyComponent.didMove.is_connected(_on_body_moved):
			bodyComponent.didMove.connect(_on_body_moved)

	if animatedSprite:
		if animatedSprite.has_signal("animation_finished") and not animatedSprite.animation_finished.is_connected(_on_animation_finished):
			animatedSprite.animation_finished.connect(_on_animation_finished)
		
		# 新增：连接帧变化信号
		if animatedSprite.has_signal("frame_changed") and not animatedSprite.frame_changed.is_connected(_on_frame_changed):
			animatedSprite.frame_changed.connect(_on_frame_changed)

func checkRequiredComponents() -> bool:
	"""检查必需的组件是否存在，并记录缺少的组件"""
	var missingComponents: Array[String] = []
	
	# 检查必需的组件
	if not coComponents.has("HealthComponent"):
		missingComponents.append("HealthComponent")
	
	if not coComponents.has("CharacterBodyComponent"):
		missingComponents.append("CharacterBodyComponent")
	
	# 敌人和 BOSS 阵营可能需要追踪方向
	if faction in [Faction.ENEMIES, Faction.BOSS]:
		if not coComponents.has("ChaseComponent") and not coComponents.has("ChasePlayerNavigationComponent"):
			printDebug("[Animation8DirectionComponent] 敌人/BOSS 阵营缺少 ChaseComponent 或 ChasePlayerNavigationComponent，将使用其他方式获取移动方向")
	
	# 记录缺少的组件
	if not missingComponents.is_empty():
		printDebug("[Animation8DirectionComponent] 缺少组件: " + str(missingComponents) + " - 组件将继续工作但功能可能受限")
		return false
	else:
		printDebug("[Animation8DirectionComponent] 所有必需组件都已找到")
		return true


func getCurrentMovementDirection() -> Vector2:
	var direction: Vector2 = Vector2.ZERO
	# 此处优先级
	# 1.敌人永远朝向玩家，从ChaseComponent或ChasePlayerNavigationComponent获取
	# 2.物理处理，从OverheadPhysicsComponent获取
	# 3.通过速度向量判断，从CharacterBodyComponent获取
	
	# enemies/boss - 只有在敌人或BOSS阵营且ChaseComponent或ChasePlayerNavigationComponent存在时才尝试获取
	if faction in [Faction.ENEMIES, Faction.BOSS]:
		direction = getMovementDirectionFromChase()
		printEnemiesDirection(1, direction)
	
	# OverheadPhysicsComponent
	if direction.is_zero_approx():
		direction = getMovementDirectionFromPhysics()
		printEnemiesDirection(2, direction)
	
	# CharacterBodyComponent
	if direction.is_zero_approx():
		direction = getMovementDirectionFromBody()
		printEnemiesDirection(3, direction)

	printEnemiesDirection(4, direction)
	return direction

func printEnemiesDirection(type: int, direction: Vector2) -> void:
	if faction in [Faction.ENEMIES, Faction.BOSS]:
		#printDebug("[Animation8DirectionComponent] 八方向组件" + str(type) + ":" + str(direction))
		pass
		
func isPlayerMoving() -> bool:
	if coComponents.has("CharacterBodyComponent"):
		var bodyComponent: CharacterBodyComponent = coComponents.CharacterBodyComponent
		if bodyComponent and bodyComponent.has_method("get") and bodyComponent.get("body"):
			var body: CharacterBody2D = bodyComponent.body
			if body and body.has_method("get"):
				var velocity: Vector2 = body.velocity
				return velocity.length() > 10.0
	return false  # 如果没有body组件或body，认为没有移动

func getMovementDirectionFromPhysics() -> Vector2:
	if coComponents.has("OverheadPhysicsComponent"):
		var physicsComponent: OverheadPhysicsComponent = coComponents.OverheadPhysicsComponent
		if physicsComponent:
			if physicsComponent.has_method("get") and physicsComponent.get("lastDirection"):
				var lastDirection: Vector2 = physicsComponent.lastDirection
				if not lastDirection.is_zero_approx():
					return lastDirection
			
			if physicsComponent.has_method("get") and physicsComponent.get("lastInputDirection"):
				var lastInputDirection: Vector2 = physicsComponent.lastInputDirection
				if not lastInputDirection.is_zero_approx():
					return lastInputDirection.normalized()
	
	return Vector2.ZERO


func getMovementDirectionFromBody() -> Vector2:
	if coComponents.has("CharacterBodyComponent"):
		var bodyComponent: CharacterBodyComponent = coComponents.CharacterBodyComponent
		if bodyComponent and bodyComponent.has_method("get") and bodyComponent.get("body"):
			var body: CharacterBody2D = bodyComponent.body
			if body and body.has_method("get"):
				var velocity: Vector2 = body.velocity
				if velocity.length() > 10.0:  # 最小移动阈值
					return velocity.normalized()
	
	return Vector2.ZERO


func getMovementDirectionFromChase() -> Vector2:
	# 优先检查 ChasePlayerNavigationComponent（用于南瓜等敌人）
	printDebug("getMovementDirectionFromChase")
	if coComponents.has("ChasePlayerNavigationComponent"):
		printDebug("getMovementDirectionFromChase: ChasePlayerNavigationComponent")
		var chaseNavComponent = coComponents.ChasePlayerNavigationComponent
		# 安全地检查组件是否有必要的方法和属性
		if chaseNavComponent.has_method("get") and chaseNavComponent.get("recentDirection"):
			var recentDirection: Vector2 = chaseNavComponent.recentDirection
			printDebug("getMovementDirectionFromChase: recentDirection=" + str(recentDirection))
			if not recentDirection.is_zero_approx():
				return recentDirection
	
	# 然后检查 ChaseComponent（用于其他敌人）
	if coComponents.has("ChaseComponent"):
		var chaseComponent: ChaseComponent = coComponents.ChaseComponent
		# 安全地检查组件是否有必要的方法和属性
		if chaseComponent.has_method("get") and chaseComponent.get("recentChaseDirection"):
			var recentChaseDirection: Vector2 = chaseComponent.recentChaseDirection
			if not recentChaseDirection.is_zero_approx():
				return recentChaseDirection
		# 如果组件存在但没有 recentChaseDirection 属性，尝试其他方法
		elif chaseComponent.has_method("getChaseDirection"):
			var chaseDirection: Vector2 = chaseComponent.getChaseDirection()
			if not chaseDirection.is_zero_approx():
				return chaseDirection
	
	return Vector2.ZERO


func getAnimationFromDirection(direction: Vector2, state: AnimationState) -> Dictionary:
	# print("getAnimationFromDirection 当前状态: ", state, ", 输入方向: ", direction)
	if direction.length() < 0.1:
		direction = currentDirection  # 使用上次方向

	var angle: float = rad_to_deg(atan2(direction.y, direction.x))
	if angle < 0: angle += 360

	var directionSuffix: String
	var shouldFlip: bool = false

	# 8方向映射（45度间隔）
	if angle >= 337.5 or angle < 22.5:
		# RIGHT (0°)
		directionSuffix = "right"
		shouldFlip = false
	elif angle >= 22.5 and angle < 67.5:
		# RIGHT_DOWN (45°)
		directionSuffix = "right_down"
		shouldFlip = false
	elif angle >= 67.5 and angle < 112.5:
		# DOWN (90°)
		directionSuffix = "down"
		shouldFlip = false
	elif angle >= 112.5 and angle < 157.5:
		# LEFT_DOWN (135°) = RIGHT_DOWN + flip
		directionSuffix = "right_down"
		shouldFlip = true
	elif angle >= 157.5 and angle < 202.5:
		# LEFT (180°) = RIGHT + flip
		directionSuffix = "right"
		shouldFlip = true
	elif angle >= 202.5 and angle < 247.5:
		# LEFT_UP (225°) = RIGHT_UP + flip
		directionSuffix = "right_up"
		shouldFlip = true
	elif angle >= 247.5 and angle < 292.5:
		# UP (270°)
		directionSuffix = "up"
		shouldFlip = false
	else:
		# RIGHT_UP (315°)
		directionSuffix = "right_up"
		shouldFlip = false

	var animationName: String = getStateAnimationName(state, directionSuffix)
	return {"name": animationName, "flip": shouldFlip}


func getStateAnimationName(state: AnimationState, direction: String) -> String:
	match state:
		AnimationState.BIRTH:
			return "born"  # 出生动画无方向
		AnimationState.IDLE:
			return "idle_" + direction
		AnimationState.MOVE:
			return "move_" + direction
		AnimationState.HURT:
			return "hurt_" + direction
		AnimationState.DEATH:
			return "die_" + direction
		AnimationState.ATTACK:
			return "attack_" + direction
		AnimationState.HIGHATTACK:
			return "attack_" + direction
		AnimationState.HIDE:
			return "hide_" + direction
		AnimationState.SHOW:
			return "show_" + direction
		_:
			return "idle_down"  # 默认动画为待机


func requestAnimationState(state: AnimationState, direction: Vector2, requester: String) -> void:
	if not STATE_PRIORITY.has(state):
		printWarning("Invalid animation state: " + str(state))
		return

	clearAnimationRequestByPriority(STATE_PRIORITY[state])
	
	requestedStates[requester] = {
		"state": state,
		"direction": direction,
		"priority": STATE_PRIORITY[state],
		"timestamp": Time.get_time_dict_from_system()
	}

	updateAnimationState()


func updateAnimationState() -> void:
	if requestedStates.is_empty():
		setAnimationState(defaultState, currentDirection)
		return

	var highestPriority: int = -1
	var selectedRequest: Dictionary = {}

	for requester: String in requestedStates:
		var request: Dictionary = requestedStates[requester]
		if request.priority > highestPriority:
			highestPriority = request.priority
			selectedRequest = request

	if selectedRequest:
		setAnimationState(selectedRequest.state, selectedRequest.direction)


func setAnimationState(state: AnimationState, direction: Vector2) -> void:
	var oldState: AnimationState = currentState
	currentState = state
	currentDirection = direction

	if oldState != currentState:
		animationStateChanged.emit(currentState, oldState)
		printDebug("[Animation8DirectionComponent] Animation state changed: " + str(oldState) + " -> " + str(currentState))

	playAnimationForState(state, direction)


func playAnimationForState(state: AnimationState, direction: Vector2) -> void:
	if not animatedSprite or not animatedSprite.sprite_frames:
		printWarning("AnimatedSprite2D or SpriteFrames not available")
		return

	var animationData: Dictionary = getAnimationFromDirection(direction, state)
	var animationName: String = animationData.name
	var shouldFlip: bool = animationData.flip

	if not animatedSprite.sprite_frames.has_animation(animationName):
		#printDebug("[Animation8DirectionComponent] Animation not found: " + animationName + " in: " + self.parentEntity.name)
		clearAnimationRequestByState(state)
		# 优先尝试 idle_down，如果没有再尝试 move_down
		var defaultDic: Dictionary = getAnimationFromDirection(direction, AnimationState.IDLE)
		animationName = defaultDic.name
		shouldFlip = defaultDic.flip
		if not animatedSprite.sprite_frames.has_animation(animationName):
			#printDebug("[Animation8DirectionComponent] Default IDLE Animation not found: " + animationName + " in: " + self.parentEntity.name)
			defaultDic = getAnimationFromDirection(direction, AnimationState.MOVE)
			animationName = defaultDic.name
			shouldFlip = defaultDic.flip
			if not animatedSprite.sprite_frames.has_animation(animationName):
				printDebug("[Animation8DirectionComponent] Default MOVE Animation not found: " + animationName + " in: " + self.parentEntity.name)
				return

	if shouldUpdateAnimation(animationName, shouldFlip):
		animatedSprite.flip_h = shouldFlip
		# 应用速度：优先使用下一次播放速度，否则使用默认速度
		var speed_to_apply: float = default_speed_scale
		if next_play_speed_scale >= 0.0:
			speed_to_apply = next_play_speed_scale
			next_play_speed_scale = -1.0
		animatedSprite.speed_scale = max(speed_to_apply, 0.0)
		animatedSprite.play(animationName)

		lastPlayedAnimation = animationName
		lastFlipState = shouldFlip
		
		# 重置帧信号状态，为新动画做准备
		frameReachedSignaled = false
		#printDebug("[Animation8DirectionComponent] 开始播放新动画，重置帧信号状态: " + animationName)

		animationPlayed.emit(animationName, direction, state)
		printDebug("[Animation8DirectionComponent] Playing animation: " + animationName + " (flip: " + str(shouldFlip) + ") for state: " + str(state))
		
		# 新增：如果播放的是受伤动画，发射受伤动画开始信号
		if state == AnimationState.HURT:
			hurtAnimationStarted.emit()
			printDebug("[Animation8DirectionComponent] 受伤动画开始播放，发射hurtAnimationStarted信号")


func shouldUpdateAnimation(animationName: String, shouldFlip: bool) -> bool:
	if not enableAnimationCache:
		return true

	return lastPlayedAnimation != animationName or lastFlipState != shouldFlip

## 设置默认播放速度（立即生效，后续所有播放均使用，除非设置了下一次播放速度）
func set_default_speed_scale(speed: float) -> void:
	default_speed_scale = max(speed, 0.0)
	if animatedSprite:
		animatedSprite.speed_scale = default_speed_scale

## 设置下一次播放速度（仅对下一次play生效，play后自动重置）
func set_next_play_speed_scale(speed: float) -> void:
	next_play_speed_scale = max(speed, 0.0)

## 便捷接口：设置下一次攻击动画播放速度
func set_next_attack_speed_scale(speed: float) -> void:
	set_next_play_speed_scale(speed)


func clearAnimationRequest(requester: String) -> void:
	#printDebug("[Animation8DirectionComponent] 清除动画请求1: " + requester)
	#printDebug("[Animation8DirectionComponent] 清除动画请求2: " + str(requestedStates))
	if requestedStates.has(requester):
		#printDebug("[Animation8DirectionComponent] 清除动画请求: " + requester)
		requestedStates.erase(requester)
		updateAnimationState()


## 通过AnimationState清除动画请求
func clearAnimationRequestByState(state: AnimationState) -> void:
	var requesterString: String = getRequesterStringFromState(state)
	if requesterString != "":
		clearAnimationRequest(requesterString)

## 清除比指定状态优先级低的状态的所有动画
func clearAnimationRequestByPriority(priority: int) -> void:
	for requester: String in requestedStates:
		var request: Dictionary = requestedStates[requester]
		if request.priority < priority:
			clearAnimationRequest(requester)

## 将AnimationState转换为对应的请求者字符串
func getRequesterStringFromState(state: AnimationState) -> String:
	match state:
		AnimationState.BIRTH:
			return "BirthSystem"
		AnimationState.IDLE:
			return "IdleSystem"
		AnimationState.MOVE:
			return "MovementSystem"
		AnimationState.HURT:
			return "DamageSystem"
		AnimationState.DEATH:
			return "DeathSystem"
		AnimationState.ATTACK:
			return "AttackSystem"
		AnimationState.HIGHATTACK:
			return "AttackSystem"
		AnimationState.HIDE:
			return "VisibilitySystem"
		AnimationState.SHOW:
			return "VisibilitySystem"
		_:
			printWarning("Unknown AnimationState: " + str(state))
			return ""


func playBirthAnimation(speed_scale: float = -1.0) -> void:
	if speed_scale >= 0.0:
		set_next_play_speed_scale(speed_scale)
	requestAnimationState(AnimationState.BIRTH, Vector2.ZERO, "BirthSystem")


func playIdle() -> void:
	var direction: Vector2 = getCurrentMovementDirection()
	playIdleAnimation(direction)
	
func playIdleAnimation(direction: Vector2, speed_scale: float = -1.0) -> void:
	if speed_scale >= 0.0:
		set_next_play_speed_scale(speed_scale)
	requestAnimationState(AnimationState.IDLE, direction, "IdleSystem")


func playMoveAnimation(direction: Vector2, speed_scale: float = -1.0) -> void:
	if speed_scale >= 0.0:
		set_next_play_speed_scale(speed_scale)
	requestAnimationState(AnimationState.MOVE, direction, "MovementSystem")


func playHurtAnimation(direction: Vector2 = Vector2.ZERO, speed_scale: float = -1.0) -> void:
	# BOSS 阵营：受击不播放受击动画，不打断行动/攻击
	if faction == Faction.BOSS:
		printDebug("[Animation8DirectionComponent] BOSS 阵营受击，不播放受击动画，不打断当前行动")
		return
	if direction.length() < 0.1:
		direction = currentDirection
	if speed_scale >= 0.0:
		set_next_play_speed_scale(speed_scale)
	requestAnimationState(AnimationState.HURT, direction, "DamageSystem")


func playDeathAnimation(direction: Vector2 = Vector2.ZERO, speed_scale: float = -1.0) -> void:
	if direction.length() < 0.1:
		direction = currentDirection
	if speed_scale >= 0.0:
		set_next_play_speed_scale(speed_scale)
	requestAnimationState(AnimationState.DEATH, direction, "DeathSystem")
	
	
func playAttackAnimation(direction: Vector2, speed_scale: float = -1.0) -> void:
	if speed_scale >= 0.0:
		set_next_play_speed_scale(speed_scale)
	requestAnimationState(AnimationState.ATTACK, direction, "AttackSystem")


func playHighAttack() -> void:
	var direction: Vector2 = getCurrentMovementDirection()
	playHighAttackAnimation(direction)

func playHighAttackAnimation(direction: Vector2, speed_scale: float = -1.0) -> void:
	if speed_scale >= 0.0:
		set_next_play_speed_scale(speed_scale)
	requestAnimationState(AnimationState.HIGHATTACK, direction, "AttackSystem")

func getCurrentState() -> AnimationState:
	return currentState


func getCurrentDirection() -> Vector2:
	return currentDirection


func getCurrentAnimation() -> String:
	return lastPlayedAnimation


func isFlipped() -> bool:
	return lastFlipState


func getAvailableAnimations() -> Array[String]:
	if not animatedSprite or not animatedSprite.sprite_frames:
		return []
	return animatedSprite.sprite_frames.get_animation_names()


func hasDeathAnimation() -> bool:
	"""检查是否有以'die'开头的死亡动画"""
	if not animatedSprite or not animatedSprite.sprite_frames:
		return false
	
	var animationNames: PackedStringArray = animatedSprite.sprite_frames.get_animation_names()
	for animName: String in animationNames:
		if animName.begins_with("die"):
			return true
	return false


func isEnemy() -> bool:
	"""检查是否为敌人阵营"""
	return faction == Faction.ENEMIES


func isPlayer() -> bool:
	"""检查是否为玩家阵营"""
	return faction == Faction.PLAYERS


func getFaction() -> Faction:
	"""获取当前阵营"""
	return faction


func setFaction(newFaction: Faction) -> void:
	"""设置阵营"""
	faction = newFaction

## 新增：设置目标帧数
func setTargetFrameNumber(frameNumber: int) -> void:
	"""设置目标帧数，当动画播放到该帧时发射信号"""
	targetFrameNumber = frameNumber
	printDebug("[Animation8DirectionComponent] 设置目标帧数: " + str(frameNumber))

## 新增：获取目标帧数
func getTargetFrameNumber() -> int:
	"""获取当前设置的目标帧数"""
	return targetFrameNumber

## 新增：重置帧信号状态
func resetFrameSignalState() -> void:
	"""重置帧信号状态，允许重新发射帧信号"""
	frameReachedSignaled = false
	printDebug("[Animation8DirectionComponent] 重置帧信号状态")


func _on_health_decreased(_difference: int) -> void:
	var direction: Vector2 = getCurrentMovementDirection()
	playHurtAnimation(direction)


func _on_health_zero() -> void:
	# 此处停止移动
	if coComponents.has("CharacterBodyComponent"):
		var bodyComponent: CharacterBodyComponent = coComponents.CharacterBodyComponent
		if bodyComponent:
			# 设置shouldMoveThisFrame为false来停止移动
			bodyComponent.shouldMoveThisFrame = false
			# 也可以将velocity设置为零来立即停止
			if bodyComponent.body:
				bodyComponent.body.velocity = Vector2.ZERO
	
	# 此处关闭伤害组件，防止播放死亡动画时继续造成伤害
	if coComponents.has("DamageComponent"):
		var damageComponent: DamageComponent = coComponents.DamageComponent
		if damageComponent:
			damageComponent.isEnabled = false
	
	# 播放死亡动画
	var direction: Vector2 = getCurrentMovementDirection()
	playDeathAnimation(direction)


func _on_body_moved(_delta: float) -> void:
	var direction: Vector2 = getCurrentMovementDirection()
	if not direction.is_zero_approx():
		if isPlayerMoving():
			playMoveAnimation(direction)
		else:
			clearAnimationRequest("MovementSystem")
			playIdleAnimation(direction)
	else:
		playIdleAnimation(direction)


func _on_animation_finished() -> void:
	var completedState: AnimationState = currentState
	printDebug("[Animation8DirectionComponent] 动画播放完成，状态: " + str(completedState) + " 动画名称: " + (animatedSprite.animation if animatedSprite else "无"))
	animationCompleted.emit(completedState)
	
	# 重置帧信号状态
	frameReachedSignaled = false

	match completedState:
		AnimationState.BIRTH:
			clearAnimationRequest("BirthSystem")
			requestAnimationState(AnimationState.IDLE, currentDirection, "DefaultSystem")

		AnimationState.HURT:
			clearAnimationRequest("DamageSystem")

		AnimationState.ATTACK:
			clearAnimationRequest("AttackSystem")

		AnimationState.HIGHATTACK:
			clearAnimationRequest("AttackSystem")

		AnimationState.DEATH:
			parentEntity.requestDeletion()
			pass

		AnimationState.IDLE:
			pass

		AnimationState.MOVE:
			printDebug("[Animation8DirectionComponent] 移动动画播放完成，清除移动请求")
			clearAnimationRequest("MovementSystem")

		AnimationState.HIDE:
			printDebug("[Animation8DirectionComponent] 隐藏动画播放完成，清除可见性请求")
			clearAnimationRequest("VisibilitySystem")

		AnimationState.SHOW:
			printDebug("[Animation8DirectionComponent] 显示动画播放完成，清除可见性请求")
			clearAnimationRequest("VisibilitySystem")

## 新增：帧变化监听方法
func _on_frame_changed() -> void:
	if not animatedSprite:
		return
	if currentState != concernState:
		return
	var currentFrame = animatedSprite.frame
	var anim_name: String = animatedSprite.animation if animatedSprite else ""
	var frames_count: int = 0
	if animatedSprite and animatedSprite.sprite_frames and anim_name != "" and animatedSprite.sprite_frames.has_animation(anim_name):
		frames_count = animatedSprite.sprite_frames.get_frame_count(anim_name)
	var last_index: int = max(frames_count - 1, 0)
	var should_emit: bool = false
	# 若目标帧超出动画帧数，则在最后一帧触发；否则在目标帧触发
	if frames_count > 0:
		if targetFrameNumber <= last_index:
			should_emit = (currentFrame == targetFrameNumber)
		else:
			should_emit = (currentFrame == last_index)
	else:
		# 无法获取帧总数时，退回原有逻辑
		should_emit = (currentFrame == targetFrameNumber)
	
	# 检查是否到达应触发的帧
	if should_emit and not frameReachedSignaled:
		frameReachedSignaled = true
		# 注意：保持对外发出的 frameNumber 为目标帧（即使超出，将在最后一帧触发），避免上层等值判断失效
		printDebug("[Animation8DirectionComponent] 动画播放到触发帧(目标=" + str(targetFrameNumber) + ", 实际=" + str(currentFrame) + ")，发射信号: " + lastPlayedAnimation)
		animationFrameReached.emit(targetFrameNumber, lastPlayedAnimation, currentState)

	if currentFrame == targetFrameNumber2 and not frameReachedSignaled2:
		printDebug("[Animation8DirectionComponent] 动画播放到第" + str(targetFrameNumber2) + "帧，发射信号: " + lastPlayedAnimation)
		frameReachedSignaled2 = true
		animationFrameReached.emit(targetFrameNumber2, lastPlayedAnimation, currentState)

	# 如果动画重新开始（帧数从0开始），重置信号状态
	if currentFrame == 0 and frameReachedSignaled:
		frameReachedSignaled = false
		frameReachedSignaled2 = false
		printDebug("[Animation8DirectionComponent] 动画重新开始，重置帧信号状态" + lastPlayedAnimation)
	

func playHideAnimation(direction: Vector2 = Vector2.ZERO, speed_scale: float = -1.0) -> void:
	"""播放隐藏动画"""
	if direction.length() < 0.1:
		direction = currentDirection
	if speed_scale >= 0.0:
		set_next_play_speed_scale(speed_scale)
	requestAnimationState(AnimationState.HIDE, direction, "VisibilitySystem")


func playShowAnimation(direction: Vector2 = Vector2.ZERO, speed_scale: float = -1.0) -> void:
	"""播放显示动画"""
	if direction.length() < 0.1:
		direction = currentDirection
	if speed_scale >= 0.0:
		set_next_play_speed_scale(speed_scale)
	requestAnimationState(AnimationState.SHOW, direction, "VisibilitySystem")
