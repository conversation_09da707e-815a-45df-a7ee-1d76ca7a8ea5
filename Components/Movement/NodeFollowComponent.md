# NodeFollowComponent 使用说明

## 概述

`NodeFollowComponent` 是一个继承自 `IndependentPathFollowComponent` 的节点跟随组件。它通过缓存目标节点的历史轨迹，生成动态路径，让实体能够跟随目标节点移动，适用于编队飞行、跟随移动等场景。

## 特性

- **智能路径生成**：实时缓存目标节点的移动轨迹
- **精确跟随**：使用现有的路径跟随机制确保平滑跟随
- **灵活配置**：支持多种跟随参数调整
- **性能优化**：可配置的更新频率和缓存长度
- **自动清理**：自动管理路径缓存和资源清理

## 参数说明

### 基础参数

- **target_node** (Node2D): 要跟随的目标节点
  - 默认值：null
  - 必须设置才能开始跟随

- **follow_distance** (float): 跟随距离（在路径上的偏移）
  - 默认值：50.0
  - 值越大，从子弹越落后于目标

### 路径缓存参数

- **path_cache_length** (int): 路径缓存长度（保留多少历史轨迹点）
  - 范围：10-100
  - 默认值：30
  - 影响跟随的平滑度和内存使用

- **path_point_spacing** (float): 路径点间距
  - 范围：5-50
  - 默认值：10.0
  - 值越小，路径越平滑，但性能消耗越大

- **update_frequency** (float): 路径更新频率（秒）
  - 范围：0.01-0.5
  - 默认值：0.1
  - 影响跟随的实时性

### 控制参数

- **auto_cleanup_path** (bool): 是否自动清理路径缓存
  - 默认值：true

- **speed** (float): 跟随速度（继承自父类）
  - 范围：-2000-2000
  - 默认值：100

## 信号

- **target_lost**: 当目标节点丢失时发出
- **path_updated**: 当路径更新时发出
- **didCompletePath**: 当完成路径跟随时发出（继承自父类）

## 方法

### 核心方法

- **set_target(new_target: Node2D)**: 设置跟随目标
- **set_follow_distance(distance: float)**: 设置跟随距离
- **clear_path_cache()**: 清理路径缓存

### 继承方法

- **reset()**: 重置组件状态
- **set_speed(speed: float)**: 设置跟随速度

## 使用方法

### 1. 基本使用

```gdscript
# 在实体场景中添加 NodeFollowComponent
# 配置参数：
target_node = master_bullet  # 设置跟随目标
follow_distance = 50.0       # 跟随距离
speed = 150.0               # 跟随速度
```

### 2. 动态设置目标

```gdscript
# 获取组件并设置目标
var node_follow = entity.get_node("NodeFollowComponent")
node_follow.set_target(master_bullet)
node_follow.set_follow_distance(100.0)
```

### 3. 监听事件

```gdscript
# 监听目标丢失事件
node_follow.target_lost.connect(_on_target_lost)

# 监听路径更新事件
node_follow.path_updated.connect(_on_path_updated)
```

## 实现原理

### 1. 路径缓存机制
- 定期记录目标节点的位置到 `path_cache` 数组
- 形成一条连续的轨迹路径
- 从子弹沿着这条历史轨迹移动

### 2. 路径更新逻辑
- 只有当目标移动距离超过 `path_point_spacing` 时才添加新点
- 保持缓存长度限制，避免内存无限增长
- 每次更新都重新生成路径

### 3. 跟随效果
- 将缓存的位置点转换为 `Curve2D`
- 设置从子弹的 `progress` 为路径长度减去 `follow_distance`
- 利用父类的路径跟随机制实现平滑移动

## 示例

### SnakeBossSlaveEntity 配置

```gdscript
[node name="NodeFollowComponent" parent="." instance=ExtResource("5_node_follow")]
speed = 150.0
follow_distance = 50.0
path_cache_length = 30
path_point_spacing = 10.0
update_frequency = 0.1
```

### 编队发射逻辑

```gdscript
# 发射主子弹
var master_bullet = fire()

# 发射从子弹
for i in range(2):
    var slave = slave_scene.instantiate()
    get_tree().current_scene.add_child(slave)
    
    var node_follow = slave.get_node_or_null("NodeFollowComponent")
    if node_follow:
        node_follow.set_target(master_bullet)
        node_follow.set_follow_distance(30.0 + (i * 30.0))
```

## 性能优化建议

1. **调整更新频率**：根据需要的实时性调整 `update_frequency`
2. **优化缓存长度**：根据跟随距离调整 `path_cache_length`
3. **设置合适的点间距**：平衡平滑度和性能消耗
4. **及时清理资源**：组件会自动清理Path2D资源

## 注意事项

1. 组件会自动创建和管理Path2D资源
2. 目标节点必须存在且有效才能正常工作
3. 跟随距离不应超过路径缓存长度
4. 组件继承自 `IndependentPathFollowComponent`，具有相同的路径跟随功能
5. 调试模式下会输出详细的路径信息
