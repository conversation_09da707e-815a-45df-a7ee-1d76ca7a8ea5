## 实现螺旋运动：线性运动的中心点加上围绕该移动中心的圆形运动
## 线性运动提供前进方向，圆形运动在其周围形成螺旋轨迹
## 适用于需要螺旋轨迹的子弹、导弹等实体

class_name CircularMotionComponent
extends Component


#region Export Parameters

## 中心点的线性运动速度（像素/秒）
@export_range(10, 1000, 10) var linearSpeed: float = 200

## 圆形运动的半径（像素）
@export_range(10, 200, 5) var radius: float = 50

## 圆形运动的角速度（弧度/秒）
@export_range(0.1, 10.0, 0.1) var angularSpeed: float = 2.0

## 自定义前进方向（当 relativeToRotation 为 false 时使用）
@export var forwardDirection: Vector2 = Vector2.RIGHT

## 圆形运动是否顺时针
@export var clockwise: bool = false

## 是否相对于实体的旋转方向
@export var relativeToRotation: bool = true

## 是否启用组件
@export var isEnabled: bool = true:
	set(newValue):
		isEnabled = newValue
		self.set_physics_process(isEnabled)

## 最大移动距离（0 表示无限制）
@export_range(0, 2000, 50) var maxDistance: float = 0

## 达到最大距离时是否删除实体
@export var deleteAtMaxDistance: bool = false

#endregion


#region State Variables

## 运动的累计时间
var time: float = 0.0

## 线性中心点移动的距离
var distanceTraveled: float = 0.0

## 组件启动时的初始位置
var startPosition: Vector2

## 是否正在移动
var isMoving: bool = true

#endregion


#region Signals

## 达到最大距离时发出
signal reachedMaxDistance

## 位置发生变化时发出
signal position_changed(new_position: Vector2)

#endregion


func _ready() -> void:
	startPosition = parentEntity.global_position
	self.set_physics_process(isEnabled)


func _physics_process(delta: float) -> void:
	if not isEnabled or not isMoving:
		return
	
	# 检查最大距离限制
	if maxDistance > 0 and distanceTraveled >= maxDistance:
		if deleteAtMaxDistance:
			parentEntity.queue_free()
		else:
			isMoving = false
		reachedMaxDistance.emit()
		return
	
	time += delta
	
	# 计算前进方向
	var currentForwardDirection: Vector2
	if relativeToRotation:
		currentForwardDirection = Vector2.RIGHT.rotated(parentEntity.rotation)
	else:
		currentForwardDirection = forwardDirection.normalized()
	
	# 计算线性中心位置
	var linearCenter: Vector2 = startPosition + currentForwardDirection * linearSpeed * time
	
	# 计算圆形运动的角度
	var angle: float = angularSpeed * time
	
	# 计算圆形偏移
	var circularOffset: Vector2 = Vector2(cos(angle), sin(angle)) * radius
	
	# 应用顺时针方向（通过反转 sin 分量）
	if clockwise:
		circularOffset.y = -circularOffset.y
	
	# 计算最终位置：线性中心 + 圆形偏移
	var finalPosition: Vector2 = linearCenter + circularOffset
	
	# 更新实体位置
	parentEntity.global_position = finalPosition
	parentEntity.reset_physics_interpolation()
	
	# 发出位置变化信号
	position_changed.emit(finalPosition)
	
	# 更新线性移动距离
	var frameLinearDistance: float = linearSpeed * delta
	distanceTraveled += frameLinearDistance
	
	if debugMode:
		printDebug(str("CircularMotion: linear=", linearCenter, " offset=", circularOffset, " final=", finalPosition, " distance=", distanceTraveled))


## 重置组件状态
func reset() -> void:
	time = 0.0
	distanceTraveled = 0.0
	startPosition = parentEntity.global_position
	isMoving = true


## 设置前进方向
func setDirection(direction: Vector2) -> void:
	forwardDirection = direction.normalized()


## 设置线性速度
func setLinearSpeed(speed: float) -> void:
	linearSpeed = speed


## 设置圆形运动参数
func setCircularParameters(newRadius: float, newAngularSpeed: float) -> void:
	radius = newRadius
	angularSpeed = newAngularSpeed
