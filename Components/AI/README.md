# AI行为组件系统

## 概述

AI行为组件系统是一个模块化的AI解决方案，采用分层混合架构，结合行为树的灵活决策能力和状态机的高效执行特性。系统通过黑板模式实现层间通信，通过参数化配置支持不同类型敌人的快速创建。

**重要特性：系统不依赖特定的组件类，使用通用的组件访问方式，更加灵活和可扩展。**

## 核心特性

### 1. 分层架构
- **决策层（行为树）**: 负责高级决策和策略选择
- **执行层（状态机）**: 负责具体行为的执行和动画管理
- **通信层（黑板）**: 负责层间数据共享和事件分发

### 2. 模块化设计
- 可复用的行为节点和状态
- 参数化配置系统
- 组件化的架构设计

### 3. 与现有系统集成
- 与Comedot组件系统无缝集成
- 支持beehave行为树插件
- 可扩展的事件系统

### 4. 无依赖设计
- **不强制要求特定组件类**
- 使用通用的组件访问方式
- 通过方法名和类名动态查找组件
- 支持任何符合接口的组件

## 快速开始

### 1. 添加AI组件到实体

```gdscript
# 在任何节点中添加AIBehaviorComponent
var ai_component = AIBehaviorComponent.new()
entity.add_child(ai_component)

# 配置基本参数
ai_component.behavior_type = AIBehaviorComponent.BehaviorType.MELEE_AGGRESSIVE
ai_component.detection_range = 200.0
ai_component.attack_range = 50.0
ai_component.movement_speed = 100.0
```

### 2. 组件要求

AIBehaviorComponent**不强制要求**任何特定组件，但会根据需要自动查找：

```gdscript
# 系统会自动查找以下类型的组件（如果存在）：
# - 健康相关组件（有damage方法或HealthComponent类）
# - 移动相关组件（有move_in_direction方法或MovementComponent类）
# - 战斗相关组件（有attack方法或DamageComponent类）
```

### 3. 组件接口

系统通过以下方式查找和使用组件：

```gdscript
# 根据类名查找组件
var health_comp = get_component_by_type("HealthComponent")

# 根据方法名查找组件
var movement_comp = get_component_by_method("move_in_direction")

# 检查组件是否有特定方法
if component.has_method("attack"):
    component.attack(target)
```

## 行为类型

系统支持多种预设行为类型：

### MELEE_AGGRESSIVE
- 近战攻击型敌人
- 高攻击力，中等移动速度
- 优先攻击，其次追击

### MELEE_DEFENSIVE
- 近战防御型敌人
- 中等攻击力，较低移动速度
- 优先防御，血量低时逃跑

### RANGED_SNIPER
- 远程狙击型敌人
- 高攻击力，低移动速度
- 保持距离，远程攻击

### RANGED_MOBILE
- 远程机动型敌人
- 中等攻击力，高移动速度
- 风筝战术，边移动边攻击

### FLYING_BOMBER
- 飞行轰炸型敌人
- 中等攻击力，飞行能力
- 空中巡逻，轰炸攻击

### SUPPORT_HEALER
- 支援治疗型敌人
- 低攻击力，治疗能力
- 支援盟友，优先治疗

### CUSTOM
- 自定义行为类型
- 可加载自定义行为树文件

## 配置系统

### 1. 基础配置

```gdscript
# 在编辑器中配置
@export var behavior_type: BehaviorType = BehaviorType.MELEE_AGGRESSIVE
@export var detection_range: float = 200.0
@export var attack_range: float = 50.0
@export var movement_speed: float = 100.0
@export var attack_damage: float = 25.0
@export var attack_cooldown: float = 2.0
```

### 2. 高级配置

```gdscript
@export var behavior_tree_path: String = ""
@export var custom_parameters: Dictionary = {}
@export var debug_mode: bool = false
@export var enable_behavior_tree: bool = true
@export var enable_state_machine: bool = true
```

### 3. 运行时配置修改

```gdscript
# 切换行为类型
ai_component.switch_behavior_type(AIBehaviorComponent.BehaviorType.RANGED_SNIPER)

# 修改参数
ai_component.custom_parameters["special_ability"] = true
ai_component.custom_parameters["aggression_level"] = 0.8

# 启用/禁用AI
ai_component.set_ai_enabled(false)
```

## 状态系统

### 默认状态

系统包含以下默认状态：

- **IdleState**: 空闲状态，观察环境
- **PatrolState**: 巡逻状态，在区域内移动
- **ChaseState**: 追击状态，追击玩家
- **AttackState**: 攻击状态，执行攻击
- **FleeState**: 逃跑状态，血量低时逃跑
- **StunnedState**: 眩晕状态，被控制时

### 自定义状态

```gdscript
class_name CustomState extends AIStateBase

func _init():
    state_name = "custom"

func setup_transitions() -> void:
    # 添加状态转换
    var transition = StateTransition.create_blackboard_transition(
        self, "idle", "custom_condition", true, 5
    )
    add_transition(transition)

func enter() -> void:
    # 进入状态时的逻辑
    pass

func execute(delta: float) -> void:
    # 状态执行逻辑
    pass

func exit() -> void:
    # 退出状态时的逻辑
    pass
```

## 行为树节点

### 自定义条件节点

```gdscript
class_name CustomCondition extends Condition

func tick(actor: Node, blackboard: Blackboard) -> int:
    var ai_blackboard = blackboard as AIBlackboard
    if not ai_blackboard:
        return FAILURE
    
    # 检查条件
    if custom_condition_check():
        return SUCCESS
    else:
        return FAILURE
```

### 自定义动作节点

```gdscript
class_name CustomAction extends ActionLeaf

func tick(actor: Node, blackboard: Blackboard) -> int:
    var ai_blackboard = blackboard as AIBlackboard
    if not ai_blackboard:
        return FAILURE
    
    # 执行动作
    if perform_custom_action():
        return SUCCESS
    else:
        return RUNNING
```

## 黑板系统

### 数据访问

```gdscript
# 获取黑板数据
var player_pos = ai_component.get_blackboard_value("player_position")
var health = ai_component.get_blackboard_value("health_percentage", 1.0)

# 设置黑板数据
ai_component.set_blackboard_value("custom_flag", true)
```

### 便捷方法

```gdscript
# 获取玩家引用
var player = ai_blackboard.get_player_reference()

# 获取玩家位置
var player_pos = ai_blackboard.get_player_position()

# 获取到玩家的方向
var direction = ai_blackboard.get_direction_to_player()

# 检查是否在攻击范围内
var in_range = ai_blackboard.is_player_in_attack_range()

# 检查是否可以攻击
var can_attack = ai_blackboard.can_attack()

# 检查血量是否低
var health_low = ai_blackboard.is_health_low(0.3)
```

## 事件系统

### 事件类型

```gdscript
enum AIEventType {
    PLAYER_DETECTED,
    PLAYER_LOST,
    ATTACK_STARTED,
    ATTACK_COMPLETED,
    HEALTH_CHANGED,
    STATE_CHANGED,
    BEHAVIOR_CHANGED,
    CONFIG_UPDATED
}
```

### 事件处理

```gdscript
# 注册事件处理器
ai_component.event_dispatcher.register_handler(
    AIEventDispatcher.AIEventType.PLAYER_DETECTED,
    _on_player_detected
)

func _on_player_detected(data: Dictionary) -> void:
    var distance = data.get("distance", 0.0)
    print("Player detected at distance: ", distance)
```

## 调试支持

### 调试信息

```gdscript
# 获取调试信息
var debug_info = ai_component.get_debug_info()
print("Current state: ", debug_info["current_state"])
print("Behavior type: ", debug_info["behavior_type"])
print("Blackboard data: ", debug_info["blackboard_data"])
```

### 调试模式

```gdscript
# 启用调试模式
ai_component.debug_mode = true

# 调试信息会输出到控制台
# AI: Player detected at distance 150.0
# AI: State changed from idle to chase
# AI: Attack started
```

## 性能优化

### 1. 分层更新
- 行为树在_process中更新
- 状态机在_process中更新
- 物理数据在_physics_process中更新

### 2. 条件检查优化
- 使用黑板缓存计算结果
- 避免重复的环境检测
- 合理设置更新频率

### 3. 内存管理
- 及时清理事件处理器
- 避免循环引用
- 使用弱引用处理外部对象

## 扩展指南

### 1. 添加新的行为类型

```gdscript
# 在AIBehaviorComponent中添加新的枚举值
enum BehaviorType {
    MELEE_AGGRESSIVE,
    RANGED_SNIPER,
    CUSTOM_BEHAVIOR  # 新增
}

# 在ConfigManager中添加配置
func create_custom_behavior_config() -> Dictionary:
    return {
        "detection_range": 300.0,
        "attack_range": 100.0,
        "movement_speed": 80.0,
        "attack_damage": 20.0,
        "attack_cooldown": 2.5
    }

# 在BehaviorTreeManager中添加行为树创建
func create_custom_behavior_tree() -> void:
    # 创建自定义行为树结构
    pass
```

### 2. 添加新的状态

```gdscript
class_name NewState extends AIStateBase

func _init():
    state_name = "new_state"

func setup_transitions() -> void:
    # 设置状态转换
    pass

func enter() -> void:
    # 进入状态逻辑
    pass

func execute(delta: float) -> void:
    # 状态执行逻辑
    pass

func exit() -> void:
    # 退出状态逻辑
    pass
```

### 3. 添加新的事件类型

```gdscript
# 在AIEventDispatcher中添加新事件类型
enum AIEventType {
    PLAYER_DETECTED,
    CUSTOM_EVENT  # 新增
}

# 添加事件处理器
func _on_custom_event(data: Dictionary) -> void:
    # 处理自定义事件
    pass

# 添加便捷发送方法
func notify_custom_event(custom_data: Dictionary) -> void:
    emit_event(AIEventType.CUSTOM_EVENT, custom_data)
```

## 最佳实践

### 1. 组件设计
- 保持组件的单一职责
- 使用接口而非具体实现
- 避免组件间的直接依赖

### 2. 状态设计
- 状态应该是原子的
- 避免状态间的复杂依赖
- 使用转换条件而非硬编码逻辑

### 3. 行为树设计
- 保持节点简单
- 使用组合而非继承
- 避免深层嵌套

### 4. 配置管理
- 使用预设配置
- 验证配置数据
- 支持运行时修改

### 5. 调试和测试
- 启用调试模式进行开发
- 编写单元测试
- 使用可视化调试工具

## 故障排除

### 常见问题

1. **AI不响应**
   - 检查是否启用了AI组件
   - 验证黑板数据是否正确
   - 检查事件是否正确触发

2. **状态不转换**
   - 检查转换条件是否正确
   - 验证黑板数据是否更新
   - 确认状态优先级设置

3. **性能问题**
   - 检查更新频率设置
   - 优化条件检查逻辑
   - 使用调试工具分析性能

4. **配置不生效**
   - 验证配置数据格式
   - 检查配置应用时机
   - 确认配置管理器初始化

### 调试技巧

1. 启用debug_mode查看详细日志
2. 使用get_debug_info()获取系统状态
3. 检查黑板数据是否正确更新
4. 验证事件是否正确触发

## 无依赖设计的优势

### 1. 灵活性
- 不强制要求特定的组件类
- 可以与任何组件系统集成
- 支持自定义组件接口

### 2. 可扩展性
- 易于添加新的组件类型
- 支持动态组件发现
- 向后兼容性好

### 3. 维护性
- 减少硬编码依赖
- 降低耦合度
- 提高代码复用性

### 4. 测试友好
- 易于模拟组件行为
- 支持单元测试
- 便于调试和验证
