extends Component
class_name LineTrailComponent

@export_category('Trail')
@export var length : = 40
@export var head_color : Color = Color(0.0, 1.0, 0.0, 1.0)
@export var tail_color : Color = Color(0.0, 0.5, 0.0, 0.0)
@export var gradient_power : float = 1.5
@export var glow_intensity : float = 0.8

@export_category('Width')
@export var base_width : float = 8.0  # 基础宽度
@export var width_variation : float = 2.0  # 宽度变化范围
@export var use_width_curve : bool = false  # 是否使用宽度曲线
@export var width_curve : Curve  # 宽度变化曲线
@export var antialiased : bool = true  # 是否开启抗锯齿

@export_category('Blur')
@export var blur_radius : float = 2.0  # 模糊半径
@export var blur_samples : int = 8  # 模糊采样数

@export_category('Particles')
@export var emit_particles : bool = true
@export var particle_emit_interval : float = 0.1  # 发射间隔
@export var particle_lifetime : float = 2.0  # 粒子生命周期
@export var particle_speed : float = 50.0  # 粒子速度
@export var min_movement_threshold : float = 5.0  # 最小移动距离阈值

@onready var line_trail: Line2D = $Line2D
@onready var particles: GPUParticles2D = $GPUParticles2D

var trail_points: Array[Vector2] = []
var last_emit_time: float = 0.0
var last_position: Vector2 = Vector2.ZERO
var is_moving: bool = false

func _ready() -> void:
	# 确保Line2D节点存在
	if not line_trail:
		printError("LineTrailComponent requires a Line2D child node named 'Line2D'")
		return
	
	# 设置Line2D属性
	line_trail.default_color = head_color
	
	# 开启抗锯齿
	line_trail.antialiased = antialiased
	
	# 设置Line2D使用纹理模式以支持shader
	line_trail.texture_mode = Line2D.LINE_TEXTURE_STRETCH
	
	# 加载并应用shader
	var shader = preload("res://Assets/Shaders/line_trail_gradient.gdshader")
	line_trail.material = ShaderMaterial.new()
	line_trail.material.shader = shader
	
	# 设置粒子系统
	_setup_particles()
	
	# 设置轨迹宽度
	_update_trail_width()
	
	# 设置shader参数
	_update_shader_parameters()
	
	# 初始化位置
	if parentEntity:
		last_position = parentEntity.global_position

func _setup_particles() -> void:
	if not particles:
		return
	
	# 创建粒子材质
	var particle_material = ParticleProcessMaterial.new()
	particle_material.particle_flag_disable_z = true
	particle_material.gravity = Vector3(0, 0, 0)  # 无重力
	particle_material.initial_velocity_min = 0.0  # 无初始速度
	particle_material.initial_velocity_max = 0.0
	particle_material.angular_velocity_min = 0.0  # 无旋转
	particle_material.angular_velocity_max = 0.0
	particle_material.scale_min = 0.8
	particle_material.scale_max = 1.2
	particle_material.lifetime_randomness = 0.2  # 生命周期随机性
	particle_material.color_ramp = _create_particle_color_ramp()
	
	particles.process_material = particle_material
	particles.emitting = false  # 手动控制发射
	particles.one_shot = true  # 一次性发射
	particles.explosiveness = 0.0  # 无爆炸效果
	particles.amount = 5  # 每次发射5个粒子

func _create_particle_color_ramp() -> GradientTexture1D:
	var gradient = Gradient.new()
	gradient.add_point(0.0, Color(1, 1, 1, 1))  # 开始时完全不透明
	gradient.add_point(0.3, Color(0.9, 0.9, 0.9, 0.8))  # 30%时开始变透明
	gradient.add_point(0.7, Color(0.7, 0.7, 0.7, 0.4))  # 70%时更透明
	gradient.add_point(1.0, Color(0.5, 0.5, 0.5, 0.0))  # 结束时完全透明
	
	var gradient_texture = GradientTexture1D.new()
	gradient_texture.gradient = gradient
	return gradient_texture

func _physics_process(delta: float) -> void:
	if not line_trail or not parentEntity:
		return

	# 获取实体位置
	var current_position = parentEntity.global_position
	
	# 检查是否在移动
	var movement_distance = current_position.distance_to(last_position)
	is_moving = movement_distance > min_movement_threshold
	
	# 只有在移动时才添加轨迹点
	if is_moving:
		line_trail.add_point(current_position, 0)
		trail_points.insert(0, current_position)
		
		if line_trail.get_point_count() > length:
			line_trail.remove_point(line_trail.get_point_count() - 1)
			trail_points.pop_back()
		
		# 发射粒子
		if emit_particles and trail_points.size() > 1:
			_emit_particles_from_trail(delta)
	else:
		# 如果没有移动，清除轨迹
		if trail_points.size() > 0:
			line_trail.clear_points()
			trail_points.clear()
	
	# 更新轨迹宽度
	_update_trail_width()
	
	# 更新shader参数
	_update_shader_parameters()
	
	# 更新上次位置
	last_position = current_position

func _emit_particles_from_trail(delta: float) -> void:
	if not particles or trail_points.size() < 3:  # 至少需要3个点才发射粒子
		return
	
	last_emit_time += delta
	
	# 按间隔发射粒子
	if last_emit_time >= particle_emit_interval:
		last_emit_time = 0.0
		
		# 在轨迹的随机位置发射粒子（排除最新的点，避免在原点发射）
		var trail_index = randi_range(1, min(trail_points.size() - 1, 8))  # 从第2个点开始，最多前8个点
		var emit_position = trail_points[trail_index]
		
		# 设置粒子发射位置
		particles.global_position = emit_position
		particles.emitting = true
		
		# 短暂发射后停止
		await get_tree().create_timer(0.05).timeout
		particles.emitting = false

func _update_trail_width() -> void:
	if not line_trail:
		return
	
	# 设置基础宽度
	line_trail.width = base_width
	
	# 创建宽度曲线
	var width_curve_resource = Curve.new()
	
	if use_width_curve and width_curve:
		# 使用用户提供的曲线
		line_trail.width_curve = width_curve
	else:
		# 创建线性宽度变化曲线
		width_curve_resource.add_point(Vector2(0.0, 1.0 + width_variation / base_width))  # 头部最宽
		width_curve_resource.add_point(Vector2(1.0, 1.0))  # 尾部最窄
		line_trail.width_curve = width_curve_resource

func _update_shader_parameters() -> void:
	if line_trail and line_trail.material:
		line_trail.material.set_shader_parameter("head_color", head_color)
		line_trail.material.set_shader_parameter("tail_color", tail_color)
		line_trail.material.set_shader_parameter("gradient_power", gradient_power)
		line_trail.material.set_shader_parameter("glow_intensity", glow_intensity)
		line_trail.material.set_shader_parameter("blur_radius", blur_radius)
		line_trail.material.set_shader_parameter("blur_samples", blur_samples)

# 公共方法：设置宽度参数
func set_width_parameters(new_base_width: float, new_width_variation: float = -1.0) -> void:
	base_width = new_base_width
	if new_width_variation >= 0.0:
		width_variation = new_width_variation
	_update_trail_width()

# 公共方法：设置宽度曲线
func set_width_curve(new_curve: Curve, use_curve: bool = true) -> void:
	width_curve = new_curve
	use_width_curve = use_curve
	_update_trail_width()

# 公共方法：获取当前宽度设置
func get_width_parameters() -> Dictionary:
	return {
		"base_width": base_width,
		"width_variation": width_variation,
		"use_width_curve": use_width_curve,
		"width_curve": width_curve
	}

# 公共方法：设置抗锯齿
func set_antialiased(enabled: bool) -> void:
	antialiased = enabled
	if line_trail:
		line_trail.antialiased = enabled

# 公共方法：获取抗锯齿状态
func is_antialiased() -> bool:
	return antialiased
