## 8方向移动控制组件
## 专门为俯视角游戏设计的8方向移动控制
## 支持动画状态管理和AI控制覆盖

class_name OverheadControlComponent8Direction
extends Component

#region 参数配置
@export var shouldInvertXAxis: bool = false
@export var shouldInvertYAxis: bool = false
@export var isEnabled: bool = true
@export var deadZone: float = 0.1
@export var diagonalNormalization: bool = true
@export var enableInput: bool = true
#endregion

#region 8方向定义
enum Direction8 {
	UP = 0,           # 上
	UP_RIGHT = 1,     # 右上
	RIGHT = 2,        # 右
	DOWN_RIGHT = 3,   # 右下
	DOWN = 4,         # 下
	DOWN_LEFT = 5,    # 左下
	LEFT = 6,         # 左
	UP_LEFT = 7       # 左上
}
#endregion

#region 状态变量
var inputDirectionOverride: Vector2 = Vector2.ZERO
var inputDirection: Vector2
var lastInputDirection: Vector2
var isInputZero: bool = true
var currentDirection: int = -1
var lastDirection: int = -1

# 动画状态映射
var animationStates: Dictionary = {
	Direction8.UP: "attack_up",
	Direction8.UP_RIGHT: "attack_up_right", 
	Direction8.RIGHT: "attack_right",
	Direction8.DOWN_RIGHT: "attack_down_right",
	Direction8.DOWN: "attack_down",
	Direction8.DOWN_LEFT: "attack_down_left",    # 镜像翻转
	Direction8.LEFT: "attack_left",              # 镜像翻转
	Direction8.UP_LEFT: "attack_up_left"         # 镜像翻转
}

# 方向锁定
var directionLock: int = -1
var isDirectionLocked: bool = false

# 攻击方向锁定
var attackDirectionLock: Vector2 = Vector2.ZERO
var isAttackDirectionLocked: bool = false
var attackDirectionLockTimer: Timer
var attackDirectionLockDuration: float = 1.0  # 攻击方向锁定持续时间（秒）
var _isDead: bool = false
#endregion

#region 依赖组件
var overheadPhysicsComponent: OverheadPhysicsComponent
var healthComponent: HealthComponent
#endregion

#region 信号
signal direction_changed(newDirection: int, oldDirection: int)
#endregion

func _enter_tree() -> void:
	super._enter_tree()
	
	#printLog("OverheadControlComponent8Direction: _enter_tree() 开始")
	
	# 获取依赖组件
	overheadPhysicsComponent = coComponents.get("OverheadPhysicsComponent")
	healthComponent = coComponents.get("HealthComponent")
	if healthComponent:
		healthComponent.healthDidZero.connect(_on_health_zero)
	
	# 初始化动画状态映射
	setupDefaultAnimationStates()
	
	# 初始化攻击方向锁定定时器
	setupAttackDirectionLockTimer()
	
	#printLog("OverheadControlComponent8Direction: _enter_tree() 完成")

func _on_health_zero() -> void:
	_isDead = true


func getRequiredComponents() -> Array[Script]:
	return [OverheadPhysicsComponent]


func _physics_process(_delta: float) -> void:
	if not isEnabled or _isDead:
		return
	if enableInput:
		processInput()
	copyInputToPhysicsComponent()

## 处理玩家输入
func processInput() -> void:
	# 获取输入方向
	if inputDirectionOverride != Vector2.ZERO:
		# AI控制
		#printLog("OverheadControlComponent8Direction: 使用AI输入覆盖: " + str(inputDirectionOverride))
		self.inputDirection = inputDirectionOverride
	else:
		# 玩家输入
		var xInput: float = Input.get_axis(GlobalInput.Actions.moveLeft, GlobalInput.Actions.moveRight)
		var yInput: float = Input.get_axis(GlobalInput.Actions.moveUp, GlobalInput.Actions.moveDown)
		var rawInput: Vector2 = Vector2(xInput, yInput)
		
		self.inputDirection = rawInput

	# 应用轴反转
	if shouldInvertXAxis:
		self.inputDirection.x = -self.inputDirection.x
		#printLog("OverheadControlComponent8Direction: X轴反转后: " + str(self.inputDirection))
	if shouldInvertYAxis:
		self.inputDirection.y = -self.inputDirection.y
		#printLog("OverheadControlComponent8Direction: Y轴反转后: " + str(self.inputDirection))

	# 应用死区
	var inputLength: float = self.inputDirection.length()
	#printLog("OverheadControlComponent8Direction: 输入长度: " + str(inputLength) + ", 死区: " + str(deadZone))
	
	if inputLength < deadZone:
		self.inputDirection = Vector2.ZERO
		#printLog("OverheadControlComponent8Direction: 输入在死区内，清零")

	# 对角线标准化
	if diagonalNormalization and self.inputDirection.length() > 0:
		self.inputDirection = self.inputDirection.normalized()
		#printLog("OverheadControlComponent8Direction: 对角线标准化后: " + str(self.inputDirection))

	# 重置覆盖值
	self.inputDirectionOverride = Vector2.ZERO

	# 缓存输入状态
	self.isInputZero = self.inputDirection.length() < deadZone
	if not isInputZero:
		# 检查是否有攻击方向锁定
		if not isAttackDirectionLocked:
			lastInputDirection = inputDirection
			# print("[OverheadControl8Dir] ✅ 有效输入: ", inputDirection)


	# 计算8方向
	updateDirection8()

## 计算8方向索引
func updateDirection8() -> void:
	if isInputZero:
		# 无输入时保持最后一个方向，不重置为-1
		#if debugMode:
			#printLog("OverheadControlComponent8Direction: 无输入，保持方向: " + str(currentDirection))
		return

	var angle: float = rad_to_deg(atan2(inputDirection.y, inputDirection.x))

	# 将角度转换为0-360度
	if angle < 0:
		angle += 360

	#printLog("OverheadControlComponent8Direction: 输入角度: " + str(angle) + "度")

	# 8方向映射 (每45度一个方向) - 修正映射
	var newDirection: int
	if angle >= 337.5 or angle < 22.5:
		newDirection = Direction8.RIGHT      # 0度
	elif angle >= 22.5 and angle < 67.5:
		newDirection = Direction8.DOWN_RIGHT # 45度 - 修正：应该是右下
	elif angle >= 67.5 and angle < 112.5:
		newDirection = Direction8.DOWN       # 90度
	elif angle >= 112.5 and angle < 157.5:
		newDirection = Direction8.DOWN_LEFT  # 135度
	elif angle >= 157.5 and angle < 202.5:
		newDirection = Direction8.LEFT       # 180度
	elif angle >= 202.5 and angle < 247.5:
		newDirection = Direction8.UP_LEFT    # 225度
	elif angle >= 247.5 and angle < 292.5:
		newDirection = Direction8.UP         # 270度
	else:
		newDirection = Direction8.UP_RIGHT   # 315度 - 修正：应该是右上

	# 检查方向锁定
	if isDirectionLocked and directionLock != -1:
		newDirection = directionLock

	# 发送方向改变信号
	if newDirection != currentDirection:
		var oldDirection: int = currentDirection
		currentDirection = newDirection
		#printLog("OverheadControlComponent8Direction: 方向改变: " + str(oldDirection) + " -> " + str(newDirection))
		#printLog("OverheadControlComponent8Direction: 方向名称: " + getDirectionName(newDirection))
		direction_changed.emit(currentDirection, oldDirection)

	lastDirection = currentDirection

## 将输入数据传递给物理组件
func copyInputToPhysicsComponent() -> void:
	if overheadPhysicsComponent:
		overheadPhysicsComponent.inputDirection = self.inputDirection
		overheadPhysicsComponent.lastInputDirection = self.lastInputDirection
		# 添加8方向支持
		if overheadPhysicsComponent.has_method("setDirection8"):
			overheadPhysicsComponent.setDirection8(self.currentDirection)


## 获取当前动画状态
func getAnimationState() -> String:
	if currentDirection == -1:
		return "idle"  # 默认待机动画
	
	# 使用当前方向获取动画，如果没有则使用最后一个方向
	var animState: String = animationStates.get(currentDirection, "")
	if animState == "" and lastDirection != -1:
		animState = animationStates.get(lastDirection, "idle")
	
	return animState if animState != "" else "idle"

## 设置默认动画状态映射
func setupDefaultAnimationStates() -> void:
	# 根据Player.tscn中现有的动画名称进行映射
	# 左方向使用右方向动画，通过翻转实现镜像效果
	animationStates = {
		Direction8.UP: "attack_up",
		Direction8.UP_RIGHT: "attack_up_right", 
		Direction8.RIGHT: "attack_right",
		Direction8.DOWN_RIGHT: "attack_down_right",
		Direction8.DOWN: "attack_down",
		Direction8.DOWN_LEFT: "attack_down_right",    # 使用右下动画+翻转
		Direction8.LEFT: "attack_right",              # 使用右动画+翻转
		Direction8.UP_LEFT: "attack_up_right"         # 使用右上动画+翻转
	}
	#printLog("OverheadControlComponent8Direction: 动画状态映射已设置")
	#printLog("动画映射: " + str(animationStates))

## 设置攻击方向锁定定时器
func setupAttackDirectionLockTimer() -> void:
	attackDirectionLockTimer = Timer.new()
	attackDirectionLockTimer.wait_time = attackDirectionLockDuration
	attackDirectionLockTimer.one_shot = true
	attackDirectionLockTimer.timeout.connect(_on_attack_direction_lock_timeout)
	add_child(attackDirectionLockTimer)
	#printLog("OverheadControlComponent8Direction: 攻击方向锁定定时器已设置")

## 攻击方向锁定定时器超时回调
func _on_attack_direction_lock_timeout() -> void:
	clearAttackDirectionLock()
	#printLog("OverheadControlComponent8Direction: 攻击方向锁定超时，自动清除")

#region 外部接口
## 设置AI输入覆盖
func setAIInput(direction: Vector2) -> void:
	inputDirectionOverride = direction

## 清除AI输入
func clearAIInput() -> void:
	inputDirectionOverride = Vector2.ZERO

## 设置方向锁定
func setDirectionLock(direction: int) -> void:
	directionLock = direction
	isDirectionLocked = true

## 清除方向锁定
func clearDirectionLock() -> void:
	directionLock = -1
	isDirectionLocked = false

## 获取当前8方向索引
func getCurrentDirection() -> int:
	return currentDirection

## 获取当前输入方向
func getInputDirection() -> Vector2:
	return inputDirection

## 检查是否有输入
func hasInput() -> bool:
	return not isInputZero

## 获取动画状态名称
func getAnimationStateName() -> String:
	return getAnimationState()

## 设置自定义动画状态映射
func setAnimationStates(newStates: Dictionary) -> void:
	animationStates = newStates

## 获取方向名称
func getDirectionName(direction: int) -> String:
	match direction:
		Direction8.UP:
			return "up"
		Direction8.UP_RIGHT:
			return "up_right"
		Direction8.RIGHT:
			return "right"
		Direction8.DOWN_RIGHT:
			return "down_right"
		Direction8.DOWN:
			return "down"
		Direction8.DOWN_LEFT:
			return "down_left"
		Direction8.LEFT:
			return "left"
		Direction8.UP_LEFT:
			return "up_left"
		-1:
			return "down"  # 无输入时默认向下
		_:
			return "down"

## 根据方向获取角度
func getDirectionAngle(direction: int) -> float:
	match direction:
		Direction8.UP:
			return 90.0
		Direction8.UP_RIGHT:
			return 45.0
		Direction8.RIGHT:
			return 0.0
		Direction8.DOWN_RIGHT:
			return 315.0
		Direction8.DOWN:
			return 270.0
		Direction8.DOWN_LEFT:
			return 225.0
		Direction8.LEFT:
			return 180.0
		Direction8.UP_LEFT:
			return 135.0
		-1:
			return 270.0  # 无输入时默认向下
		_:
			return 270.0

## 检查是否为对角线方向
func isDiagonalDirection(direction: int) -> bool:
	return (direction == Direction8.UP_RIGHT or 
			direction == Direction8.DOWN_RIGHT or 
			direction == Direction8.DOWN_LEFT or 
			direction == Direction8.UP_LEFT)

## 检查是否为直线方向
func isStraightDirection(direction: int) -> bool:
	return (direction == Direction8.UP or 
			direction == Direction8.RIGHT or 
			direction == Direction8.DOWN or 
			direction == Direction8.LEFT)

## 获取相反方向
func getOppositeDirection(direction: int) -> int:
	match direction:
		Direction8.UP:
			return Direction8.DOWN
		Direction8.UP_RIGHT:
			return Direction8.DOWN_LEFT
		Direction8.RIGHT:
			return Direction8.LEFT
		Direction8.DOWN_RIGHT:
			return Direction8.UP_LEFT
		Direction8.DOWN:
			return Direction8.UP
		Direction8.DOWN_LEFT:
			return Direction8.UP_RIGHT
		Direction8.LEFT:
			return Direction8.RIGHT
		Direction8.UP_LEFT:
			return Direction8.DOWN_RIGHT
		-1:
			return -1  # 无输入时没有相反方向
		_:
			return -1

## 设置攻击方向锁定
func setAttackDirectionLock(direction: Vector2, duration: float = -1.0) -> void:
	attackDirectionLock = direction
	isAttackDirectionLocked = true
	lastInputDirection = direction
	
	if duration > 0:
		attackDirectionLockDuration = duration
	
	if attackDirectionLockTimer:
		attackDirectionLockTimer.wait_time = attackDirectionLockDuration
		attackDirectionLockTimer.start()
	
	#printLog("OverheadControlComponent8Direction: 设置攻击方向锁定: " + str(direction) + ", 持续时间: " + str(attackDirectionLockDuration))

## 清除攻击方向锁定
func clearAttackDirectionLock() -> void:
	isAttackDirectionLocked = false
	attackDirectionLock = Vector2.ZERO
	
	if attackDirectionLockTimer:
		attackDirectionLockTimer.stop()
	
	#printLog("OverheadControlComponent8Direction: 清除攻击方向锁定")

## 检查是否有攻击方向锁定
func hasAttackDirectionLock() -> bool:
	return isAttackDirectionLocked

## 获取攻击方向锁定
func getAttackDirectionLock() -> Vector2:
	return attackDirectionLock
#endregion



func getDebugInfo() -> Dictionary:
	return {
		"inputDirection": inputDirection,
		"isInputZero": isInputZero,
		"currentDirection": currentDirection,
		"lastDirection": lastDirection,
		"animationState": getAnimationState(),
		"shouldInvertXAxis": shouldInvertXAxis,
		"shouldInvertYAxis": shouldInvertYAxis,
		"deadZone": deadZone,
		"diagonalNormalization": diagonalNormalization,
		"isDirectionLocked": isDirectionLocked,
		"directionLock": directionLock
	}

#endregion 
