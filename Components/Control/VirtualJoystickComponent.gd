## 虚拟摇杆组件 - 基于Figma设计稿优化的触摸摇杆控制
## 生成标准化的输入向量，兼容现有的PlayerInputComponent系统

class_name VirtualJoystickComponent
extends Control

#region Parameters
@export var enabled: bool = true

## 摇杆的最大拖拽半径（像素）- 基于Figma设计稿，增加拖拽范围
@export var maxRadius: float = 105.0  # 从105.665增加到150，提供更大的拖拽范围

## 摇杆是否固定在初始位置，还是跟随触摸点移动
@export var isFixed: bool = true

## 是否启用屏幕任意位置触碰激活功能
@export var enableAnywhereTouch: bool = false

## 自动回归到默认位置的动画时间（秒）
@export var returnAnimationDuration: float = 0.3

## 摇杆移动的边界边距（防止移动到屏幕边缘外）
@export var screenMargin: float = 50.0

## 摇杆的透明度
@export var joystickAlpha: float = 0.8

## 是否在没有触摸时隐藏摇杆
@export var hideWhenNotTouching: bool = false

## 输入死区，小于此值的输入将被忽略
@export var deadZone: float = 0.05

## 手柄半径 - 基于Figma设计稿
@export var knobRadius: float = 40.36  # 80.72 / 2

## 是否显示方向线
@export var showDirectionLine: bool = true

## 方向线长度
@export var directionLineLength: float = 60.0

## 摇杆背景颜色（可选，用于调试时区分背景）
@export var backgroundColor: Color = Color.TRANSPARENT

@export var debugMode: bool = false

#endregion

#region Signals
## 当摇杆输入改变时发出信号
signal inputChanged(inputVector: Vector2)

## 当开始触摸摇杆时发出信号
signal touchStarted()

## 当停止触摸摇杆时发出信号
signal touchEnded()
#endregion

#region Private Variables
var _isDragging: bool = false
var _touchIndex: int = -1
var _centerPosition: Vector2
var _currentInputVector: Vector2 = Vector2.ZERO
var _knobStartPosition: Vector2

## 默认位置相关
var _defaultPosition: Vector2  # 摇杆的默认位置
var _isAtDefaultPosition: bool = true  # 是否在默认位置

## 动画相关
var _returnTween: Tween  # 回归动画的Tween对象

## UI节点引用（从场景中获取）
var _backgroundSprite: Sprite2D
var _knobSprite: Sprite2D
var _directionLineSprite: Sprite2D
#endregion

#region Godot Lifecycle
func _ready() -> void:
	if debugMode: print("[VirtualJoystick] ========== 开始初始化 ==========")
	if debugMode: print("[VirtualJoystick] 节点路径: ", get_path())
	if debugMode: print("[VirtualJoystick] 节点名称: ", name)
	if debugMode: print("[VirtualJoystick] 父节点: ", get_parent().name if get_parent() else "无")
	if debugMode: print("[VirtualJoystick] 父节点类型: ", get_parent().get_class() if get_parent() else "无")
	
	# 添加到虚拟摇杆组，便于其他组件查找
	add_to_group("virtual_joystick")
	
	_setupUIReferences()
	_centerPosition = size / 2
	_knobStartPosition = _centerPosition
	
	# 保存默认位置
	_defaultPosition = global_position
	_isAtDefaultPosition = true
	
	# 设置背景颜色（如果指定了的话）
	if backgroundColor != Color.TRANSPARENT:
		var colorRect = ColorRect.new()
		colorRect.color = backgroundColor
		colorRect.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		colorRect.mouse_filter = Control.MOUSE_FILTER_IGNORE
		add_child(colorRect)
		move_child(colorRect, 0)  # 将背景移到最底层
	
	if hideWhenNotTouching:
		modulate.a = 0.3
	
	# 确保Control节点可以接收输入
	mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 检查是否在CanvasLayer中
	var canvas_layer = get_parent()
	while canvas_layer and not canvas_layer is CanvasLayer:
		canvas_layer = canvas_layer.get_parent()
	
	if debugMode: print("[VirtualJoystick] 初始化完成 - 尺寸: ", size, " 中心: ", _centerPosition, " 全局位置: ", global_position)
	if debugMode: print("[VirtualJoystick] 鼠标过滤器: ", mouse_filter)
	if debugMode: print("[VirtualJoystick] 在CanvasLayer中: ", canvas_layer is CanvasLayer)
	if debugMode: print("[VirtualJoystick] 可见性: ", visible)
	if debugMode: print("[VirtualJoystick] 透明度: ", modulate.a)
	if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - enableAnywhereTouch状态: ", enableAnywhereTouch)
	if debugMode: print("[VirtualJoystick] ========== 初始化完成 ==========")

# 添加定时器来检测卡住状态
var _stuckCheckTimer: float = 0.0
var _lastInputTime: float = 0.0
const STUCK_TIMEOUT: float = 2.0  # 2秒无输入则认为卡住

func _process(delta: float) -> void:
	if not enabled:
		return
	# 🚨 关键修复：禁用卡住检测机制，因为它会干扰正常的长时间拖拽
	# 检测摇杆是否卡在激活状态
	if _isDragging:
		_stuckCheckTimer += delta
		
		# 🚨 大幅增加超时时间，从2秒增加到30秒，避免正常拖拽被误判为卡住
		# 如果超过超时时间且没有新的输入，强制重置
		if _stuckCheckTimer > 30.0:  # 从2秒增加到30秒
			if debugMode: print("[VirtualJoystick] 🚨 检测到摇杆长时间无响应，强制重置")
			_forceReset()
	else:
		_stuckCheckTimer = 0.0

func _gui_input(event: InputEvent) -> void:
	if not enabled:
		return
	if debugMode: print("[VirtualJoystick] _gui_input 收到事件: ", event.get_class())
	# 使用_gui_input处理桌面鼠标事件（始终处理，不区分平台）
	if event is InputEventMouseButton:
		if debugMode: print("[VirtualJoystick] 处理鼠标按钮事件: ", event.pressed, " 位置: ", event.position)
		_handleMouseButtonEvent(event as InputEventMouseButton)
	elif event is InputEventMouseMotion:
		if debugMode: print("[VirtualJoystick] 处理鼠标移动事件: ", event.position)
		_handleMouseMotionEvent(event as InputEventMouseMotion)

func _unhandled_input(event: InputEvent) -> void:
	# 使用_unhandled_input确保在所有其他节点处理完后再处理
	# 这样可以避免被游戏场景的其他组件拦截
	if not enabled:
		return
	# 处理桌面鼠标移动事件（当摇杆处于拖拽状态时）
	if event is InputEventMouseMotion:
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - _unhandled_input 收到鼠标移动事件: ", event.position)
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - _isDragging状态: ", _isDragging)
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - _touchIndex: ", _touchIndex)
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 鼠标按钮状态: ", event.button_mask)
		
		# 如果摇杆正在拖拽中，且是鼠标左键拖拽，处理鼠标移动
		if _isDragging and _touchIndex == 0 and (event.button_mask & MOUSE_BUTTON_MASK_LEFT):
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 处理拖拽中的鼠标移动（超出摇杆区域）")
			# 将全局坐标转换为摇杆本地坐标
			var localPosition = event.position - global_position
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 转换后本地坐标: ", localPosition)
			_updateJoystick(localPosition)
			_lastInputTime = Time.get_time_dict_from_system()["second"]
			get_viewport().set_input_as_handled()
			return
	
	# 处理桌面鼠标事件（用于测试屏幕任意位置功能）
	elif event is InputEventMouseButton:
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - _unhandled_input 收到鼠标事件: ", event.pressed, " 位置: ", event.position, " 按钮: ", event.button_index)
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - enableAnywhereTouch状态: ", enableAnywhereTouch)
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - _isDragging状态: ", _isDragging)
		
		if event.button_index == MOUSE_BUTTON_LEFT:
			# 如果是鼠标释放事件，无论位置如何都要检查是否需要结束当前触摸
			if not event.pressed and _isDragging and _touchIndex == 0:
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 强制结束鼠标触摸")
				_endTouch()
				get_viewport().set_input_as_handled()
				return
			
			# 如果启用了屏幕任意位置功能，处理所有鼠标按下事件
			if enableAnywhereTouch and event.pressed and not _isDragging:
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 屏幕任意位置功能启用，开始处理鼠标点击")
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 点击位置: ", event.position)
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 摇杆当前位置: ", global_position)
				
				# 检查点击是否在虚拟摇杆区域外
				var global_rect = get_global_rect()
				if not global_rect.has_point(event.position):
					if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 点击在摇杆区域外，触发屏幕任意位置功能")
					
					# 移动摇杆到点击位置
					moveToPosition(event.position)
					if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 摇杆已移动到新位置: ", event.position)
					
					# 立即开始拖拽状态，将点击位置作为摇杆中心开始拖拽
					# 这样用户就可以立即开始拖拽摇杆
					var localPosition = _centerPosition  # 点击位置现在就是摇杆中心
					if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 立即开始拖拽状态，本地坐标: ", localPosition)
					
					# 直接设置拖拽状态，不通过_startTouch的范围检查
					_isDragging = true
					_touchIndex = 0
					_lastInputTime = Time.get_time_dict_from_system()["second"]  # 重置输入时间，防止卡住检测
					
					if hideWhenNotTouching:
						modulate.a = joystickAlpha
					
					# 🚨 关键修复：立即更新摇杆到中心位置，但不产生移动信号
					# 这样用户可以立即开始拖拽
					_updateJoystick(_centerPosition)
					
					# 发送开始信号
					if debugMode: print("[VirtualJoystick] 📡 发送touchStarted信号...")
					touchStarted.emit()
					if debugMode: print("[VirtualJoystick] 📡 touchStarted信号已发送")
					
					get_viewport().set_input_as_handled()
					return
				else:
					if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 点击在摇杆区域内，由_gui_input处理")
			else:
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 屏幕任意位置功能条件不满足:")
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - enableAnywhereTouch: ", enableAnywhereTouch)
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - event.pressed: ", event.pressed)
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - not _isDragging: ", not _isDragging)
	
	# 处理移动设备触摸事件
	elif event is InputEventScreenTouch:
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - _unhandled_input 收到触摸事件: ", event.pressed, " 位置: ", event.position, " 索引: ", event.index)
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - enableAnywhereTouch状态: ", enableAnywhereTouch)
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - _isDragging状态: ", _isDragging)
		
		# 如果是触摸结束事件，无论位置如何都要检查是否需要结束当前触摸
		if not event.pressed and _isDragging and event.index == _touchIndex:
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 强制结束触摸，索引匹配")
			_endTouch()
			get_viewport().set_input_as_handled()
			return
		
		# 如果启用了屏幕任意位置功能，处理所有触摸开始事件
		if enableAnywhereTouch and event.pressed and not _isDragging:
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 屏幕任意位置功能启用，开始处理触摸")
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 触摸位置: ", event.position)
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 摇杆当前位置: ", global_position)
			
			# 检查触摸是否在虚拟摇杆区域外
			var global_rect = get_global_rect()
			if not global_rect.has_point(event.position):
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 触摸在摇杆区域外，触发屏幕任意位置功能")
				
				# 移动摇杆到触摸位置
				moveToPosition(event.position)
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 摇杆已移动到新位置: ", event.position)
				
				# 立即开始拖拽状态，将触摸位置作为摇杆中心开始拖拽
				# 这样用户就可以立即开始拖拽摇杆
				var localPosition = _centerPosition  # 触摸位置现在就是摇杆中心
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 立即开始拖拽状态，本地坐标: ", localPosition)
				
				# 直接设置拖拽状态，不通过_startTouch的范围检查
				_isDragging = true
				_touchIndex = event.index
				_lastInputTime = Time.get_time_dict_from_system()["second"]  # 重置输入时间，防止卡住检测
				
				if hideWhenNotTouching:
					modulate.a = joystickAlpha
				
				# 🚨 关键修复：立即更新摇杆到中心位置，但不产生移动信号
				# 这样用户可以立即开始拖拽
				_updateJoystick(_centerPosition)
				
				# 发送开始信号
				if debugMode: print("[VirtualJoystick] 📡 发送touchStarted信号...")
				touchStarted.emit()
				if debugMode: print("[VirtualJoystick] 📡 touchStarted信号已发送")
				
				get_viewport().set_input_as_handled()
				return
			else:
				if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 触摸在摇杆区域内，由_gui_input处理")
		else:
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 屏幕任意位置功能条件不满足:")
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - enableAnywhereTouch: ", enableAnywhereTouch)
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - event.pressed: ", event.pressed)
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - not _isDragging: ", not _isDragging)
			
	elif event is InputEventScreenDrag:
		if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - _unhandled_input 收到拖拽事件: ", event.position, " 索引: ", event.index)
		
		# 只处理当前活跃的触摸拖拽
		if _isDragging and event.index == _touchIndex:
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 处理活跃触摸的拖拽事件")
			_handleDragEvent(event as InputEventScreenDrag)
			get_viewport().set_input_as_handled()
			_lastInputTime = Time.get_time_dict_from_system()["second"]
		else:
			if debugMode: print("[VirtualJoystick] 🔥 CRITICAL - 拖拽事件不属于当前摇杆，忽略")

#endregion

#region UI Setup
func _setupUIReferences() -> void:
	"""获取场景中已有的UI组件引用"""
	# 获取场景中的UI节点引用
	_backgroundSprite = get_node("WheelBackground") as Sprite2D
	_knobSprite = get_node("TouchKnob") as Sprite2D
	_directionLineSprite = get_node("DirectionLine") as Sprite2D
	
	# 验证节点是否存在
	if not _backgroundSprite:
		if debugMode: print("[VirtualJoystick] 错误: 未找到WheelBackground节点")
	if not _knobSprite:
		if debugMode: print("[VirtualJoystick] 错误: 未找到TouchKnob节点")
	if not _directionLineSprite:
		if debugMode: print("[VirtualJoystick] 错误: 未找到DirectionLine节点")
	
	# 设置初始状态
	if _directionLineSprite:
		# 初始状态下显示方向线，指向上方
		_directionLineSprite.visible = true
		# 设置方向线默认指向最上方（-90度，即向上）
		_directionLineSprite.rotation = -PI/2  # -90度，指向上方
		# 延迟设置位置，等_centerPosition正确设置后
		call_deferred("_setup_initial_direction_line_position")
		# 设置初始透明度
		_directionLineSprite.modulate.a = 0.6  # 稍微透明，表示默认状态
	
	# 设置透明度
	modulate.a = joystickAlpha
	
	if debugMode: print("[VirtualJoystick] UI引用设置完成 - 尺寸: ", size, " 中心: ", _centerPosition)

func _setup_initial_direction_line_position() -> void:
	"""延迟设置方向线初始位置，确保_centerPosition已正确设置"""
	if _directionLineSprite:
		# 设置初始位置在摇杆中心上方
		var initialRadius = maxRadius * 0.78  # 与_updateJoystick中的安全半径保持一致
		_directionLineSprite.position = _centerPosition + Vector2(0, -initialRadius)  # 向上方向
		if debugMode: print("[VirtualJoystick] 方向线初始位置已设置: ", _directionLineSprite.position, " 中心: ", _centerPosition, " 半径: ", initialRadius)
#endregion

#region Touch Handling
func _handleTouchEvent(event: InputEventScreenTouch) -> void:
	if not enabled:
		return
	if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - _handleTouchEvent 开始处理")
	if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 事件详情: pressed=", event.pressed, " position=", event.position, " index=", event.index)
	if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 当前拖拽状态: _isDragging=", _isDragging, " _touchIndex=", _touchIndex)
	
	if event.pressed:
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 触摸开始，调用_startTouch")
		# 移动触摸事件需要坐标转换
		var global_rect = get_global_rect()
		var localPosition = event.position - global_rect.position
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 触摸坐标转换: global=", event.position, " -> local=", localPosition)
		_startTouch(localPosition, event.index)
	else:
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 触摸结束，检查索引匹配")
		if event.index == _touchIndex:
			if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 索引匹配，调用_endTouch")
			_endTouch()
		else:
			if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 索引不匹配，忽略事件")

func _handleDragEvent(event: InputEventScreenDrag) -> void:
	if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - _handleDragEvent: position=", event.position, " index=", event.index)
	if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 拖拽状态检查: _isDragging=", _isDragging, " _touchIndex=", _touchIndex)
	
	if _isDragging and event.index == _touchIndex:
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 拖拽条件满足，调用_updateJoystick")
		# 移动触摸事件需要坐标转换
		var global_rect = get_global_rect()
		var localPosition = event.position - global_rect.position
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 触摸坐标转换: global=", event.position, " -> local=", localPosition)
		_updateJoystick(localPosition)
	else:
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 拖拽条件不满足，忽略事件")

func _handleMouseButtonEvent(event: InputEventMouseButton) -> void:
	if event.button_index == MOUSE_BUTTON_LEFT:
		if event.pressed:
			_startTouch(event.position, 0)  # 使用索引0作为鼠标输入
		else:
			if _isDragging:
				_endTouch()

func _handleMouseMotionEvent(event: InputEventMouseMotion) -> void:
	# 只处理摇杆区域内的鼠标移动事件
	# 区域外的移动事件由_unhandled_input处理，确保拖拽不会因为离开区域而中断
	if _isDragging and event.button_mask & MOUSE_BUTTON_MASK_LEFT:
		if debugMode: print("[VirtualJoystick] 🔥 GUI_INPUT - 处理摇杆区域内的鼠标移动: ", event.position)
		_updateJoystick(event.position)
		_lastInputTime = Time.get_time_dict_from_system()["second"]

func _startTouch(position: Vector2, index: int) -> void:
	if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - _startTouch 开始: position=", position, " index=", index)
	
	if _isDragging:
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 已在拖拽中，忽略新触摸")
		return
	
	# 简化坐标处理 - 桌面鼠标事件直接使用本地坐标
	var localPosition: Vector2 = position
	
	if debugMode: print("[VirtualJoystick] _startTouch: position=", position, " -> local=", localPosition)
	
	var distance = localPosition.distance_to(_centerPosition)
	
	if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 最终计算: center=", _centerPosition, " distance=", distance, " maxRadius=", maxRadius)
	if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 节点信息: global_position=", global_position, " size=", size)
	
	# 如果启用了屏幕任意位置功能，无论触碰在哪里都移动摇杆到触碰点
	if enableAnywhereTouch:
		if debugMode: print("[VirtualJoystick] 启用屏幕任意位置功能，移动摇杆到触碰点")
		# 将摇杆中心移动到触摸位置
		var touchGlobalPos = global_position + localPosition
		moveToPosition(touchGlobalPos)
		# 重新计算本地位置，现在触碰点就是摇杆中心
		localPosition = _centerPosition
		distance = 0  # 重置距离，因为现在触碰点就在中心
	
	# 检查触摸是否在摇杆范围内（经过可能的重新定位后）
	if distance <= maxRadius:
		if debugMode: print("[VirtualJoystick] Touch accepted, starting drag")
		_isDragging = true
		_touchIndex = index
		
		if hideWhenNotTouching:
			modulate.a = joystickAlpha
		
		# 先更新摇杆位置和计算输入向量，再显示方向线
		# 这样可以避免方向线在第一瞬间显示在错误位置
		_updateJoystick(localPosition)
		
		# 只有在有实际输入时才显示方向线
		if _directionLineSprite and _currentInputVector.length() > deadZone:
			_directionLineSprite.visible = true
		
		if debugMode: print("[VirtualJoystick] 📡 发送touchStarted信号...")
		touchStarted.emit()
		if debugMode: print("[VirtualJoystick] 📡 touchStarted信号已发送")
	else:
		if debugMode: print("[VirtualJoystick] Touch rejected, outside range")

func _endTouch() -> void:
	if debugMode: print("[VirtualJoystick] 🚨 _endTouch 被调用，当前状态: _isDragging=", _isDragging, " _touchIndex=", _touchIndex)
	
	if not _isDragging:
		if debugMode: print("[VirtualJoystick] 🚨 摇杆未在拖拽状态，跳过结束处理")
		return
	
	if debugMode: print("[VirtualJoystick] 🚨 开始结束触摸处理...")
	
	_isDragging = false
	_touchIndex = -1
	_stuckCheckTimer = 0.0  # 重置卡住检测计时器
	
	# 🚨 关键修复：摇杆回到原始位置
	if enableAnywhereTouch and not _isAtDefaultPosition:
		if debugMode: print("[VirtualJoystick] 🔄 摇杆回到原始位置")
		returnToDefaultPosition()
	
	# 立即清零输入向量并发送信号，确保角色停止移动
	_currentInputVector = Vector2.ZERO
	if debugMode: print("[VirtualJoystick] 📡 强制发送零向量信号确保停止移动")
	inputChanged.emit(_currentInputVector)
	
	# 重置手柄位置到中心
	if _knobSprite:
		_knobSprite.position = _centerPosition
		if debugMode: print("[VirtualJoystick] 🚨 手柄位置已重置到中心: ", _centerPosition)
	
	# 重置方向线到默认状态（显示向上）
	if _directionLineSprite:
		_directionLineSprite.visible = true  # 保持可见，显示默认方向
		_directionLineSprite.scale = Vector2.ONE
		_directionLineSprite.modulate.a = 0.6  # 默认透明度
		_directionLineSprite.rotation = -PI/2  # 重置为默认向上方向
		# 重置位置到默认向上位置（轮盘边缘）
		var initialRadius = maxRadius * 0.78  # 使用78%的半径，与_updateJoystick保持一致
		_directionLineSprite.position = _centerPosition + Vector2(0, -initialRadius)
		if debugMode: print("[VirtualJoystick] 🚨 方向线已重置到默认向上方向")
	
	if hideWhenNotTouching:
		modulate.a = 0.3
	
	if debugMode: print("[VirtualJoystick] 📡 发送touchEnded信号...")
	touchEnded.emit()
	if debugMode: print("[VirtualJoystick] 📡 touchEnded信号已发送")
	if debugMode: print("[VirtualJoystick] 🚨 触摸结束处理完成")

# 强制重置摇杆状态（用于处理卡住情况）
func _forceReset() -> void:
	if debugMode: print("[VirtualJoystick] 🚨🚨 执行强制重置...")
	
	_isDragging = false
	_touchIndex = -1
	_stuckCheckTimer = 0.0
	_currentInputVector = Vector2.ZERO
	
	# 强制发送零向量信号
	inputChanged.emit(_currentInputVector)
	
	# 重置所有UI状态
	if _knobSprite:
		_knobSprite.position = _centerPosition
	
	if _directionLineSprite:
		_directionLineSprite.visible = true  # 保持可见，显示默认方向
		_directionLineSprite.scale = Vector2.ONE
		_directionLineSprite.modulate.a = 0.6  # 默认透明度
		_directionLineSprite.rotation = -PI/2  # 重置为默认向上方向
		# 重置位置到默认向上位置（轮盘边缘）
		var initialRadius = maxRadius * 0.80 # 使用80%的半径，在轮盘边缘位置
		_directionLineSprite.position = _centerPosition + Vector2(0, -initialRadius)
	
	if hideWhenNotTouching:
		modulate.a = 0.3
	
	# 发送结束信号
	touchEnded.emit()
	
	if debugMode: print("[VirtualJoystick] 🚨🚨 强制重置完成")

func _updateJoystick(inputPosition: Vector2) -> void:
	# 简化坐标处理 - 桌面鼠标事件直接使用本地坐标
	var localPosition: Vector2 = inputPosition
	
	if debugMode: print("[VirtualJoystick] _updateJoystick: inputPos=", inputPosition, " -> local=", localPosition)
	
	var offset = localPosition - _centerPosition
	var distance = offset.length()
	
	if debugMode: print("[VirtualJoystick] Update计算: local=", localPosition, " center=", _centerPosition, " offset=", offset, " distance=", distance)
	
	# 限制在最大半径内
	var clampedOffset = offset
	if distance > maxRadius:
		clampedOffset = offset.normalized() * maxRadius
		distance = maxRadius
	
	# 计算TouchKnob的实际可移动半径（背景半径 - 触点半径）
	var knobMaxRadius = maxRadius - knobRadius
	var knobOffset = clampedOffset
	if clampedOffset.length() > knobMaxRadius:
		knobOffset = clampedOffset.normalized() * knobMaxRadius
	
	# 更新手柄位置（确保触点不超出背景区域）
	if _knobSprite:
		_knobSprite.position = _centerPosition + knobOffset
		if debugMode: print("[VirtualJoystick] TouchKnob position updated to: ", _knobSprite.position, " (offset: ", knobOffset, ")")
	
	# 计算标准化的输入向量（基于原始offset，不是knobOffset）
	var newInputVector = Vector2.ZERO
	if distance > deadZone:  # 使用像素距离而不是归一化距离作为死区
		# 修复Y轴方向 - Godot屏幕坐标Y向下为正，但游戏中向下移动应该是正Y
		newInputVector = Vector2(clampedOffset.x / maxRadius, -clampedOffset.y / maxRadius)
		if debugMode: print("[VirtualJoystick] 🔥 DIRECTION DEBUG - 原始offset: ", clampedOffset, " -> 修正后向量: ", newInputVector)
	
	# 更新方向线 - 只有在拖拽状态且有实际输入时才显示和更新
	if _directionLineSprite and showDirectionLine and _isDragging and newInputVector.length() > deadZone:
		# 确保方向线可见
		if not _directionLineSprite.visible:
			_directionLineSprite.visible = true
		# 计算方向线的角度
		var angle = clampedOffset.angle()
		_directionLineSprite.rotation = angle
		
		# 获取方向线图片的实际尺寸
		var directionLineTexture = _directionLineSprite.texture
		var directionLineSize = Vector2.ZERO
		if directionLineTexture:
			directionLineSize = directionLineTexture.get_size()
		
		# 计算方向线的有效半径（考虑图片尺寸）
		var directionLineRadius = directionLineSize.length() * 0.3  # 减小安全边距
		var safeRadius = maxRadius - directionLineRadius  # 安全半径，确保方向线不超出边缘
		
		# 调整安全半径，让方向线稍微往里一点点
		# 从85%调整到80%，让方向线更靠近轮盘内侧
		if safeRadius < maxRadius * 0.78:  # 降低到80%，让方向线往里一点
			safeRadius = maxRadius * 0.78
		else:
			safeRadius = maxRadius * 0.78  # 稍微往里调整
		
		# 将方向线定位到调整后的安全半径上
		var directionLinePosition = _centerPosition + clampedOffset.normalized() * safeRadius
		_directionLineSprite.position = directionLinePosition
		
		# 方向线长度固定，不进行缩放
		_directionLineSprite.scale = Vector2.ONE
		
		# 直接显示轮盘线原颜色，不根据距离调整透明度
		_directionLineSprite.modulate.a = 1.0
		
		if debugMode: print("[VirtualJoystick] DirectionLine - angle: ", rad_to_deg(angle), "°, position: ", _directionLineSprite.position, ", alpha: 1.0, safe radius: ", safeRadius, ", direction line radius: ", directionLineRadius)
	
	# 降低阈值，提高响应灵敏度，或者在向量为零时总是发送信号
	var threshold = 0.005  # 降低阈值提高灵敏度
	
	# 🚨 关键修复：确保零向量总是被发送，防止卡住状态
	var should_emit = false
	if newInputVector.length() == 0.0:
		# 零向量总是发送，确保停止移动
		should_emit = true
		if debugMode: print("[VirtualJoystick] 🚨 强制发送零向量，确保停止移动")
	elif _currentInputVector.length() == 0.0 and newInputVector.length() > 0.0:
		# 从零向量变为非零向量，总是发送
		should_emit = true
		if debugMode: print("[VirtualJoystick] 🚨 从静止开始移动，发送新向量")
	elif newInputVector.distance_to(_currentInputVector) > threshold:
		# 向量变化超过阈值
		should_emit = true
		if debugMode: print("[VirtualJoystick] 📡 向量变化超过阈值，发送信号")
	
	if should_emit:
		_currentInputVector = newInputVector
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 📡 输入向量变化: ", _currentInputVector)
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 📡 发送inputChanged信号到适配器...")
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 📡 信号连接数量: ", inputChanged.get_connections().size())
		inputChanged.emit(_currentInputVector)
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 📡 inputChanged信号已发送")
	else:
		if debugMode: print("[VirtualJoystick] 🔥 MOBILE DEBUG - 📡 输入向量变化太小，跳过信号发送")
#endregion

#region Public Methods
## 获取当前的输入向量
func getInputVector() -> Vector2:
	return _currentInputVector

## 获取当前输入（兼容性方法）
func getCurrentInput() -> Vector2:
	return _currentInputVector

## 获取水平输入值
func getHorizontalInput() -> float:
	return _currentInputVector.x

## 获取垂直输入值
func getVerticalInput() -> float:
	return _currentInputVector.y  # Y轴已经在_updateJoystick中修正，直接返回

## 重置摇杆到中心位置
func resetJoystick() -> void:
	if debugMode: print("[VirtualJoystick] 🔧 公共方法resetJoystick被调用")
	_forceReset()

## 强制重置摇杆（公共接口）
func forceReset() -> void:
	if debugMode: print("[VirtualJoystick] 🔧 公共方法forceReset被调用")
	_forceReset()

## 强制清理所有状态（用于游戏暂停/恢复时调用）
func forceCleanup() -> void:
	if debugMode: print("[VirtualJoystick] 🔧 强制清理所有状态")
	
	# 强制重置所有状态
	_isDragging = false
	_touchIndex = -1
	_stuckCheckTimer = 0.0
	_currentInputVector = Vector2.ZERO
	
	# 强制发送零向量信号，确保停止移动
	if debugMode: print("[VirtualJoystick] 📡 强制发送零向量信号停止移动")
	inputChanged.emit(_currentInputVector)
	
	# 重置UI状态
	if _knobSprite:
		_knobSprite.position = _centerPosition
		if debugMode: print("[VirtualJoystick] 🔧 手柄位置重置到中心: ", _centerPosition)
	
	if _directionLineSprite:
		_directionLineSprite.visible = true  # 保持可见，显示默认方向
		_directionLineSprite.scale = Vector2.ONE
		_directionLineSprite.modulate.a = 0.6  # 默认透明度
		_directionLineSprite.rotation = -PI/2  # 重置为默认向上方向
		# 重置位置到默认向上位置（轮盘边缘）
		var initialRadius = maxRadius * 0.78  # 使用78%的半径，与_updateJoystick保持一致
		_directionLineSprite.position = _centerPosition + Vector2(0, -initialRadius)
		if debugMode: print("[VirtualJoystick] 🔧 方向线重置到默认向上方向")
	
	# 重置透明度
	if hideWhenNotTouching:
		modulate.a = 0.3
	
	# 如果不在默认位置，回归到默认位置
	if enableAnywhereTouch and not _isAtDefaultPosition:
		returnToDefaultPosition()
	
	# 发送结束信号
	touchEnded.emit()
	
	if debugMode: print("[VirtualJoystick] 🔧 强制清理完成")

## 设置摇杆的可见性
func setVisible(visible: bool) -> void:
	self.visible = visible

## 设置摇杆透明度
func setAlpha(alpha: float) -> void:
	joystickAlpha = alpha
	modulate.a = alpha

## 设置是否显示方向线
func setShowDirectionLine(show: bool) -> void:
	showDirectionLine = show
	if _directionLineSprite:
		_directionLineSprite.visible = show and _isDragging

## 设置摇杆背景颜色
func setBackgroundColor(color: Color) -> void:
	backgroundColor = color
	
	# 移除现有的背景
	for child in get_children():
		if child is ColorRect:
			child.queue_free()
	
	# 如果指定了新的背景颜色，添加新的背景
	if backgroundColor != Color.TRANSPARENT:
		var colorRect = ColorRect.new()
		colorRect.color = backgroundColor
		colorRect.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		colorRect.mouse_filter = Control.MOUSE_FILTER_IGNORE
		add_child(colorRect)
		move_child(colorRect, 0)  # 将背景移到最底层

## 计算输入向量（用于测试和调试）
func _calculateInputVector(touchPos: Vector2) -> Vector2:
	var localPosition = touchPos - global_position
	var offset = localPosition - _centerPosition
	var distance = offset.length()
	
	# 限制在最大半径内
	if distance > maxRadius:
		offset = offset.normalized() * maxRadius
	
	# 计算标准化的输入向量，修复Y轴方向
	var inputVector = Vector2(offset.x / maxRadius, -offset.y / maxRadius)
	
	# 应用死区
	if inputVector.length() < deadZone:
		inputVector = Vector2.ZERO
	
	return inputVector

## 处理输入变化（内部方法，供适配器调用）
func _onInputChanged(inputVector: Vector2) -> void:
	_currentInputVector = inputVector
	inputChanged.emit(inputVector)

## 获取摇杆状态信息
func getJoystickInfo() -> Dictionary:
	return {
		"is_dragging": _isDragging,
		"input_vector": _currentInputVector,
		"center_position": _centerPosition,
		"max_radius": maxRadius,
		"knob_radius": knobRadius,
		"dead_zone": deadZone,
		"is_at_default_position": _isAtDefaultPosition,
		"default_position": _defaultPosition,
		"enable_anywhere_touch": enableAnywhereTouch
	}

## 移动摇杆到指定位置（用于屏幕任意位置触碰激活）
func moveToPosition(newPosition: Vector2) -> void:
	if not enableAnywhereTouch:
		return
	
	# 计算安全位置，确保摇杆不会移动到屏幕边缘外
	# 将摇杆中心对准触碰位置
	var centeredPosition = newPosition - size / 2
	var safePosition = _calculateSafePosition(centeredPosition)
	
	# 停止当前的回归动画
	if _returnTween:
		_returnTween.kill()
	
	# 移动摇杆到新位置
	global_position = safePosition
	_centerPosition = size / 2  # 重新计算中心位置
	_isAtDefaultPosition = false
	
	if debugMode: print("[VirtualJoystick] 摇杆移动到新位置: ", safePosition, " (居中调整)")

## 回归到默认位置
func returnToDefaultPosition() -> void:
	if _isAtDefaultPosition:
		return
	
	if debugMode: print("[VirtualJoystick] 开始回归到默认位置: ", _defaultPosition)
	
	# 停止当前的回归动画
	if _returnTween:
		_returnTween.kill()
	
	# 创建新的回归动画
	_returnTween = create_tween()
	_returnTween.set_ease(Tween.EASE_OUT)
	_returnTween.set_trans(Tween.TRANS_CUBIC)
	
	# 动画移动到默认位置
	_returnTween.tween_property(self, "global_position", _defaultPosition, returnAnimationDuration)
	
	# 动画完成后的回调
	_returnTween.tween_callback(_onReturnAnimationCompleted)

## 计算安全位置，确保摇杆不会移动到屏幕边缘外
func _calculateSafePosition(targetPosition: Vector2) -> Vector2:
	var viewport = get_viewport()
	if not viewport:
		return targetPosition
	
	var screenSize = viewport.get_visible_rect().size
	var joystickSize = size
	
	# 计算边界
	var minX = screenMargin
	var minY = screenMargin
	var maxX = screenSize.x - joystickSize.x - screenMargin
	var maxY = screenSize.y - joystickSize.y - screenMargin
	
	# targetPosition 已经是调整后的位置，直接限制在安全范围内
	var safePosition = Vector2(
		clamp(targetPosition.x, minX, maxX),
		clamp(targetPosition.y, minY, maxY)
	)
	
	return safePosition

## 回归动画完成的回调
func _onReturnAnimationCompleted() -> void:
	_isAtDefaultPosition = true
	_centerPosition = size / 2  # 重新计算中心位置
	if debugMode: print("[VirtualJoystick] 回归到默认位置完成")

## 设置默认位置
func setDefaultPosition(newDefaultPosition: Vector2) -> void:
	_defaultPosition = newDefaultPosition
	if _isAtDefaultPosition:
		global_position = _defaultPosition

## 获取默认位置
func getDefaultPosition() -> Vector2:
	return _defaultPosition

## 检查是否在默认位置
func isAtDefaultPosition() -> bool:
	return _isAtDefaultPosition

## 启用或禁用屏幕任意位置触碰激活功能
func setEnableAnywhereTouch(enable: bool) -> void:
	enableAnywhereTouch = enable
	if not enable and not _isAtDefaultPosition:
		# 如果禁用功能且不在默认位置，立即回归
		returnToDefaultPosition()

## 强制立即回到默认位置（无动画）
func snapToDefaultPosition() -> void:
	if _returnTween:
		_returnTween.kill()
	
	global_position = _defaultPosition
	_centerPosition = size / 2
	_isAtDefaultPosition = true
	if debugMode: print("[VirtualJoystick] 立即回到默认位置")
#endregion
