## 通用发射组件 - 继承 CooldownComponent，支持任何类型的攻击实体
## 
## 通过 bulletEntity PackedScene 和 fireMode 配置支持激光、闪电、子弹等任何攻击实体类型
## 支持单一实体（SINGLE）和多实体（MULTIPLE）两种发射模式

class_name FireComponent
extends CooldownComponent

#region 发射模式枚举

## 发射模式枚举
enum FireMode {
	SINGLE,         ## 单发射击模式 - 实体销毁，才会创建新的实体，使用场景：一次性箭矢
	MULTIPLE,       ## 连发射击模式 - 允许多个实体同时存在，使用场景：连发射击子弹
}

#endregion

#region 导出属性
@export var fireMode: FireMode = FireMode.SINGLE

## 攻击实体场景 - 支持任何继承自 Entity 的 PackedScene
@export var bulletEntity: PackedScene

## 攻击实体配置资源 - 用于设置实体的 export 属性
@export var bulletConfig: EntityConfig

## 是否启用组件
@export var isEnabled: bool = true

## 自动射击模式
@export var autoFire: bool = false

## 按下开火模式 - 按下 fire action 开火，松开停止开火
@export var enablePressFire: bool = false

## 发射出去子弹是否需要跟随旋转: 激光，喷火武器，发射出去也需要旋转
@export var enableBulletRotateWithGun: bool = false


#endregion


#region 通用发射状态

## 攻击实体列表 - 维护所有当前存在的攻击实体
var bulletEntityList: Array[Entity] = []

## 按下开火状态 - 记录当前是否正在按下开火
var isPressingFire: bool = false

#endregion

#region 内部节点引用
@onready var fireEmitter: Marker2D = $Pivot/GunSprite/BulletEmitter
#endregion


#region 信号

## 发射开始信号
signal fireStarted(entity: Entity)

## 发射完成信号
signal fireCompleted(entity: Entity)

## 弹药耗尽信号
signal didDepleteAmmo

## 弹药不足信号
signal ammoInsufficient

#endregion

#region 依赖组件

var labelComponent: LabelComponent:
	get:
		if not labelComponent: labelComponent = self.coComponents.get(&"LabelComponent")
		return labelComponent

#endregion


#region 生命周期

func _ready() -> void:
	super._ready()

	
			
func _process(_delta: float) -> void:
	if not isEnabled: return
	# 处理自动射击（优先级高于按下开火）
	if autoFire:
		fire()
	# 处理按下开火模式（仅在非自动射击模式下）
	elif enablePressFire and isPressingFire:
		fire()

func _exit_tree() -> void:
	# 清理所有子弹实体
	cleanupAllBulletEntities()

## 处理输入事件 - 用于按下开火模式
func _input(event: InputEvent) -> void:
	if not isEnabled or not enablePressFire:
		return
		
	# 检测 fire action 的按下和松开
	if event.is_action_pressed("fire"):
		# fire action 按下，开始开火
		isPressingFire = true
		if debugMode: printDebug("Fire action pressed - starting fire")
	elif event.is_action_released("fire"):
		# fire action 松开，停止开火
		isPressingFire = false
		if debugMode: printDebug("Fire action released - stopping fire")
	
#endregion


#region 核心发射控制

## 开始发射攻击 - 根据发射模式分发到不同策略
func fire(config: EntityConfig = bulletConfig, bullet: PackedScene = bulletEntity) -> Entity:

	if not canFire():
		return null
		
	bulletConfig = config
	bulletEntity = bullet
	
	if not bulletEntity:
		printWarning("No bulletEntity specified!")
		return null
		
	# 创建新的攻击实体
	var entity = createEntity()
	if not entity: return null
	
	startCooldown()
	
	return entity

## 创建攻击实体（虚拟方法，可被子类重写）
func createEntity() -> Entity:
	var	newEntity = bulletEntity.instantiate() as Entity
	
	# 配置并添加到场景
	configureEntity(newEntity)
	addEntityToScene(newEntity)

	if not newEntity:
		printError("Cannot instantiate bullet entity: " + bulletEntity.resource_path)
		return null
	
	if debugMode: printDebug(str("createEntity() → ", newEntity))
	return newEntity


## 配置攻击实体（虚拟方法，可被子类重写）
func configureEntity(entity: Entity) -> void:
	if not entity: return
	# 应用 export 属性配置
	if bulletConfig and bulletConfig.applyToEntity(entity):
		if debugMode: printDebug(str("Applied config resource to entity: ", entity.name))
	
	# 调试标志
	if not entity.isLoggingEnabled: entity.isLoggingEnabled = self.debugMode
	if not entity.debugMode: entity.debugMode = self.debugMode
	
	# 使子弹忽略父级变换，防止父节点后续旋转影响飞行方向
	entity.z_index = fireEmitter.z_index
	entity.top_level = !enableBulletRotateWithGun
	## 初始化子弹的全局位置与旋转与发射器一致，确保直线方向正确
	entity.global_position = fireEmitter.global_position
	entity.global_rotation = fireEmitter.global_rotation
	
	# 监听实体信号
	entity.ready.connect(_on_bullet_entity_ready.bind(entity))
	entity.tree_entered.connect(_on_bullet_entity_tree_entered.bind(entity))
	entity.tree_exited.connect(_on_bullet_entity_tree_exited.bind(entity))
	entity.tree_exiting.connect(_on_entity_tree_exiting.bind(entity))
	
	# 伤害组件设置
	var entityDamageComponent: DamageComponent = entity.components.get(&"DamageComponent")
	if entityDamageComponent: entityDamageComponent.initiatorEntity = self.parentEntity
	
	# 阵营设置
	var gunFactionComponent: FactionComponent = self.coComponents.get(&"FactionComponent")
	var entityFactionComponent: FactionComponent = entity.components.get(&"FactionComponent")
	
	if gunFactionComponent and entityFactionComponent:
		if debugMode: printDebug(str("Copying entity factions to bullet: ", gunFactionComponent.factions))
		entityFactionComponent.factions = gunFactionComponent.factions


## 将攻击实体添加到场景
func addEntityToScene(entity: Entity) -> void:
	if not entity: return
	fireEmitter.add_child(entity, false)
	entity.owner = entity.get_parent()

#region 信号处理

## 攻击实体进入场景树时调用
func _on_bullet_entity_tree_entered(entity: Entity) -> void:
	if not entity:
		if debugMode: printDebug(str("Invalid entity entered tree"))
		return
		
	if debugMode: printDebug(str("Bullet entity entered scene tree: ", entity))
	fireStarted.emit(entity)
	# 添加到实体列表之中
	bulletEntityList.append(entity)


## 攻击实体准备好时调用
func _on_bullet_entity_ready(entity: Entity) -> void:
	if not is_instance_valid(entity):
		return

	if debugMode: printDebug(str("Bullet entity initialized at emitter transform: pos=", entity.global_position, ", rot=", entity.global_rotation))
	

## 攻击实体离开场景树时调用
func _on_bullet_entity_tree_exited(entity: Entity) -> void:
	if not entity:
		if debugMode: printDebug(str("Invalid entity exited tree"))
		return
		
	if debugMode: printDebug(str("Bullet entity exited scene tree: ", entity))
	fireCompleted.emit(entity)
	# 从实体列表中移除
	bulletEntityList.erase(entity)

## 实体即将离开场景树时的备用清理机制
func _on_entity_tree_exiting(entity: Entity) -> void:
	if debugMode: printDebug(str("Entity tree_exiting backup cleanup: ", entity))
	# 确保实体从列表中移除
	if entity in bulletEntityList:
		bulletEntityList.erase(entity)
		if debugMode: printDebug(str("Backup cleanup removed entity from bulletEntityList: ", entity))

#endregion


#region 公共接口

## 清理所有子弹实体
func cleanupAllBulletEntities() -> void:
	if debugMode: printDebug(str("Cleaning up all bullet entities. Count: ", bulletEntityList.size()))
	
	# 遍历并删除所有子弹实体
	for entity in bulletEntityList:
		if is_instance_valid(entity):
			if debugMode: printDebug(str("Removing bullet entity: ", entity))
			entity.queue_free()
	
	# 清空列表
	bulletEntityList.clear()
	
	if debugMode: printDebug("All bullet entities cleaned up")

## 检查是否可以发射 - 根据发射模式提供不同的判断逻辑
func canFire() -> bool:
	if not isEnabled:
		return false
		
	# 冷却未完成
	if not hasCooldownCompleted:
		return false
		
	# 连续射击和单发射击，同一时间只会存在一个实体
	if fireMode == FireMode.SINGLE and not bulletEntityList.is_empty():
		return false
		
	return true

## 获取当前发射模式
func getCurrentFireMode() -> FireMode:
	return fireMode


## 获取发射模式名称（用于调试）
func getFireModeName() -> String:
	match fireMode:
		FireMode.SINGLE:
			return "SINGLE"
		FireMode.MULTIPLE:
			return "MULTIPLE"
		_:
			return "UNKNOWN"

## 手动开始按下开火（用于外部控制）
func startPressFire() -> void:
	if enablePressFire:
		isPressingFire = true
		if debugMode: printDebug("Press fire started manually")

## 手动停止按下开火（用于外部控制）
func stopPressFire() -> void:
	if enablePressFire:
		isPressingFire = false
		if debugMode: printDebug("Press fire stopped manually")

## 检查是否正在按下开火
func isCurrentlyPressingFire() -> bool:
	return isPressingFire

#endregion

#region 配置验证
