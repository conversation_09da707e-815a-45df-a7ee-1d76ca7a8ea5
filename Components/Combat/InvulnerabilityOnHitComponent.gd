## Grants temporary immunity to damage after the Entity's [DamageReceivingComponent] takes damage.
## Useful for combat systems based on "lives"; a finite number of discrete hits that the player can take.
## Requirements: [AnimationPlayer]

class_name InvulnerabilityOnHitComponent
extends Component

# NOTE: DESIGN: Why not use a [HealthComponent]?
# REASON: Because a [HealthComponent] may lose health from damage-over-time sources like poison.
# This kind of "temporary invulnerability" is intended for one-shot damage from enemies, for now.

# TODO: Proper animations for visual effects


#region Parameters
@export var isEnabled: bool = true
@export var waitTime: float = 1.5

@export var animationPlayer:	AnimationPlayer ## If `null`, the first matching [AnimationPlayer] child the entity is used.
@export var animationOnStart:	StringName = &"invulnerabilityOnHit"
@export var animationOnEnd:		StringName = &"RESET"
#endregion


#region Signals
signal didStartInvulnerability
signal didEndInvulnerability
#endregion


#region State

@onready var timer: Timer = $InvulnerabilityTimer

@onready var damageReceivingComponent: DamageReceivingComponent = coComponents.DamageReceivingComponent # TBD: Static or dynamic?

var isActive: bool:
	get: return not timer.is_stopped()

#endregion


func _ready() -> void:
	timer.wait_time = waitTime
	damageReceivingComponent.didReceiveDamage.connect(self.damageReceivingComponent_didReceiveDamage)
	if not animationPlayer:
		self.animationPlayer = parentEntity.findFirstChildOfType(AnimationPlayer)


#region Invulnerability

func damageReceivingComponent_didReceiveDamage(_damageComponent: DamageComponent, amount: int, _attackerFactions: int) -> void:
	if not isEnabled: return

	# TBD: Activat invulnerability only if there is some actual damage.
	if amount <= 0: return

	startInvulnerability()


func onTimerTimeout() -> void:
	endInvulnerability()


func startInvulnerability() -> void:
	if not parentEntity: return

	timer.start()

	if damageReceivingComponent:
		damageReceivingComponent.isEnabled = false
		# Avoid cases where damage-per-second seems too quick when invulnerability ends.
		# TBD: Should this be handled a better way?
		damageReceivingComponent.accumulatedFractionalDamage = 0

	applyVisualEffect()
	didStartInvulnerability.emit()


func endInvulnerability() -> void:
	timer.stop()
	damageReceivingComponent.isEnabled = true
	parentEntity.modulate = Color.WHITE
	removeVisualEffect()
	didEndInvulnerability.emit()

#endregion


#region Visual Effects

func applyVisualEffect() -> void:
	if animationPlayer and not animationOnStart.is_empty():
		animationPlayer.play(animationOnStart)


func removeVisualEffect() -> void:
	if animationPlayer:
		# Stop the "start" animation even if there is no "end" animation to play.
		animationPlayer.play("RESET")
		if not animationOnEnd.is_empty():
			animationPlayer.play(animationOnEnd)

#endregion
